// import React, {useState, useEffect} from 'react';
// import {Text, StyleSheet, View, TouchableOpacity, Image} from 'react-native';
// import {Colors} from '../../constants/colors';
// import CustomerListItem from './CustomerListItem';
// import DraggableFlatList, {
//   RenderItemParams,
//   ScaleDecorator,
// } from 'react-native-draggable-flatlist';
// import {Swipeable} from 'react-native-gesture-handler';
// import {imagePath} from '../../constants/imagePath';
// import {Customer} from '.';

// interface CustomerListProps {
//   customers: Customer[];
//   onSelectCustomer: (customer: Customer) => void;
//   onEditCustomer?: (id: string) => void;
//   onDeleteCustomer?: (id: string) => void;
//   onReorderCustomers?: (customers: Customer[]) => void;
// }

// const CustomerList = ({
//   customers,
//   onSelectCustomer,
//   onEditCustomer,
//   onDeleteCustomer,
//   onReorderCustomers,
// }: CustomerListProps): React.JSX.Element => {
//   const [data, setData] = useState(customers);

//   useEffect(() => {
//     setData(customers);
//   }, [customers]);

//   const renderRightActions = (item: any) => {
//     return (
//       <TouchableOpacity
//         onPress={() => onDeleteCustomer?.(item.id)}
//         style={styles.deleteButton}>
//         <Image
//           source={imagePath.delete_Icon}
//           style={styles.actionIcon}
//           resizeMode="contain"
//         />
//       </TouchableOpacity>
//     );
//   };

//   const renderItem = ({item, drag, isActive}: RenderItemParams<any>) => (
//     <ScaleDecorator>
//       <Swipeable renderRightActions={() => renderRightActions(item)}>
//         <View
//           style={[
//             styles.draggableItem,
//             {
//               backgroundColor: isActive ? Colors.greyBackground : Colors.white,
//             },
//           ]}>
//           <CustomerListItem
//             item={item}
//             onPress={() => onSelectCustomer(item)}
//             onEdit={() => onEditCustomer?.(item.id)}
//             onDelete={() => onDeleteCustomer?.(item.id)}
//             onDrag={drag}
//             isDragging={isActive}
//           />
//         </View>
//       </Swipeable>
//     </ScaleDecorator>
//   );

//   return (
//     <>
//       {data.length === 0 ? (
//         <Text style={styles.emptyText}>No customers found</Text>
//       ) : (
//         <DraggableFlatList
//           data={data}
//           keyExtractor={item => item.customerId}
//           onDragEnd={({data: newData}) => {
//             setData(newData);
//             onReorderCustomers?.(newData);
//           }}
//           renderItem={renderItem}
//           contentContainerStyle={styles.listContent}
//           dragItemOverflow={true}
//         />
//       )}
//     </>
//   );
// };

// const styles = StyleSheet.create({
//   listContent: {
//     padding: 16,
//   },
//   emptyText: {
//     textAlign: 'center',
//     fontSize: 16,
//     color: Colors.listSeconary,
//     marginTop: 40,
//   },
//   draggableItem: {
//     marginBottom: 8,
//     borderRadius: 8,
//     overflow: 'hidden',
//   },
//   deleteButton: {
//     backgroundColor: '#A9A9A9',
//     justifyContent: 'center',
//     alignItems: 'flex-end',
//     paddingHorizontal: 18,
//     marginVertical: 8,
//     borderRadius: 8,
//     bottom: 3,
//   },
//   deleteText: {
//     color: '#fff',
//     fontWeight: 'bold',
//     fontSize: 16,
//   },
//   actionIcon: {
//     width: 20,
//     height: 20,
//     tintColor: Colors.white,
//   },
// });

// export default CustomerList;
