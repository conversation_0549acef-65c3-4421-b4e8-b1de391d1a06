import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import {Colors} from '../../constants/colors';
import {imagePath} from '../../constants/imagePath';
import {useNavigation} from '@react-navigation/native';

interface DeliveryListHeaderProps {
  search: string;
  title: string;
  setSearch: (query: string) => void;
  onSave?: () => void;
  isDragEnabled: boolean;
  showSearchInput: boolean;
  setShowSearchInput: (sI: boolean) => void;
}

const DeliveryListHeader = ({
  title,
  setSearch,
  onSave,
  isDragEnabled,
  search,
  showSearchInput,
  setShowSearchInput,
}: DeliveryListHeaderProps): React.JSX.Element => {
  const navigation = useNavigation();
  const handleBackPress = () => {
    navigation.goBack();
  };

  const toggleSearchInput = () => {
    setShowSearchInput(!showSearchInput);
    if (showSearchInput) {
      setSearch('');
    }
  };

  const handleSearch = (text: string) => {
    setSearch(text);
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
          <Image source={imagePath.back_arrow_icon} style={styles.icon} />
        </TouchableOpacity>
      </View>

      {showSearchInput && !isDragEnabled ? (
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search customers..."
            value={search}
            onChangeText={handleSearch}
            autoFocus
            placeholderTextColor={Colors.white}
          />
        </View>
      ) : (
        <Text style={styles.title}>{title}</Text>
      )}

      {isDragEnabled ? (
        <TouchableOpacity onPress={onSave} style={styles.startButton}>
          <Text style={styles.startButtonText}>Save</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.rightContainer}>
          <TouchableOpacity
            onPress={toggleSearchInput}
            style={styles.iconButton}>
            <Image source={imagePath.search_Icon} style={styles.icon} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.primary,
    height: 60,
    paddingHorizontal: 16,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
    flex: 1,
    textAlign: 'center',
  },
  iconButton: {
    padding: 8,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
  searchContainer: {
    flex: 1,
    marginHorizontal: 8,
  },
  searchInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    color: Colors.white,
  },
  startButton: {
    backgroundColor: '#FFBC00',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 5,
  },
  startButtonText: {
    color: Colors.black,
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default DeliveryListHeader;
