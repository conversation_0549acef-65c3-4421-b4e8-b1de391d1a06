// import React from 'react';
// import {
//   FlatList,
//   Text,
//   StyleSheet,
//   View,
//   ActivityIndicator,
// } from 'react-native';
// import {Colors} from '../../../constants/colors';
// import Row from '../CustomerRow';

// const List = ({
//   customers,
//   onSelectCustomer,
//   onEditCustomer,
//   onDeleteCustomer,
//   isLoading,
// }) => {
//   if (isLoading) {
//     return (
//       <View style={styles.centerContainer}>
//         <ActivityIndicator size="large" color={Colors.primary} />
//       </View>
//     );
//   }

//   if (customers.length === 0) {
//     return (
//       <View style={styles.centerContainer}>
//         <Text style={styles.emptyText}>No customers found</Text>
//       </View>
//     );
//   }

//   return (
//     <FlatList
//       data={customers}
//       keyExtractor={item => item.id.toString()}
//       renderItem={({item}) => (
//         <Row
//           customer={item}
//           onPress={() => onSelectCustomer(item)}
//           onEdit={() => onEditCustomer(item.id)}
//           onDelete={() => onDeleteCustomer(item.id)}
//         />
//       )}
//       contentContainerStyle={styles.listContent}
//       ItemSeparatorComponent={() => <View style={styles.separator} />}
//     />
//   );
// };

// const styles = StyleSheet.create({
//   listContent: {
//     paddingBottom: 80, // Space for FAB
//   },
//   centerContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     padding: 20,
//   },
//   emptyText: {
//     fontSize: 16,
//     color: Colors.grey,
//     textAlign: 'center',
//   },
//   separator: {
//     height: 1,
//     backgroundColor: Colors.lightGrey,
//     marginHorizontal: 16,
//   },
// });

// export default List;
