import React, {useState} from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {Colors} from '../../constants/colors';
import {imagePath} from '../../constants/imagePath';
import {Customer} from '.';
import ImageCarouselModal from '../../components/ImageCarouselModal';
import { IMAGE_API_BASE_URL } from '../../constants/api';

interface RowProps {
  customer: Customer;
  enableDrag: boolean;
}

const Row = ({customer, enableDrag}: RowProps): React.JSX.Element => {
  console.log(customer, 'csusjdaTa');
  const [modalVisible, setModalVisible] = useState(false);

  // Extract base64 image data
 const imageUris = React.useMemo(() => {
  if (!Array.isArray(customer?.images) || !customer?.customerCode) return [];
  return customer.images
    .filter(img => img?.imageName)
    .map(img => `${IMAGE_API_BASE_URL}/${customer.customerCode}/${img.imageName}`);
}, [customer]);

  return (
    <View style={[styles.deliveryItem]}>
      <View>
        <TouchableOpacity
          onPress={() => imageUris.length > 0 && setModalVisible(true)}>
          {imageUris.length > 0 ? (
            <Image
              source={{uri: imageUris[0]}}
              style={styles.customerImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.customerImagePlaceholder}>
              <Image
                source={imagePath.person_Icon}
                style={styles.placeholderIcon}
                resizeMode="contain"
              />
            </View>
          )}
        </TouchableOpacity>
        <ImageCarouselModal
          visible={modalVisible}
          images={imageUris}
          onClose={() => setModalVisible(false)}
        />
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <Text style={styles.customerName}>{customer.name}</Text>
          <Text style={styles.dateText}>{customer.email}</Text>
          <Text style={styles.dateText}>{customer.mobile}</Text>
          <Text style={styles.dateText}>
            Sequence {customer?.customerRouteDetail?.sequence}
          </Text>
        </View>
        {enableDrag && <Image source={imagePath.drag_indicator} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  deliveryItem: {
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 10,
  },
  customerImage: {
    width: 85,
    height: 85,
    borderRadius: 5,
    backgroundColor: Colors.greyBackground,
  },
  customerImagePlaceholder: {
    width: 85,
    height: 85,
    borderRadius: 5,
    backgroundColor: Colors.greyBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.greyText,
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'column',
    marginBottom: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.dark,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: Colors.white,
    fontWeight: 'bold',
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  icon: {
    width: 16,
    height: 16,
    marginRight: 8,
    tintColor: Colors.greyText,
  },
  addressText: {
    fontSize: 14,
    color: Colors.greyText,
    flex: 1,
  },
  dateText: {
    fontSize: 14,
    color: Colors.greyText,
  },
  actionsContainer: {
    justifyContent: 'center',
  },
  actionButton: {
    padding: 8,
  },
  actionIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.greyText,
  },
  dot: {
    width: 5,
    height: 5,
    borderRadius: 3,
    margin: 5,
  },
});

export default Row;
