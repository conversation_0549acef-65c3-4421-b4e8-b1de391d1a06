import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Image,
} from 'react-native';
import {Colors} from '../../../constants/colors';
import {imagePath} from '../../../constants/imagePath';
import {FontFamily} from '../../../constants/fontFamily';
import {fetchStoreRoutes} from '../../../redux/thunks/regionThunks';
import {useAppDispatch} from '../../../redux/hooks';

interface OptionType {
  label: string;
  value: string;
  regionId?: string;
}

interface DropdownProps {
  label: string;
  value: string;
  options: OptionType[];
  onSelect: (routeId: string, regionId?: string) => void;
  error?: string;
  touched?: boolean;
}

const Dropdown = ({
  label,
  value,
  options,
  onSelect,
  error,
  touched,
}: DropdownProps): React.JSX.Element => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<OptionType | null>(null);
  const [routes, setRoutes] = useState<OptionType[]>([]);
  const [isRouteListVisible, setIsRouteListVisible] = useState(false);

  const selectedOption = options.find(option => option.value === value);
  const dispatch = useAppDispatch();

  // Reset state when modal is opened
  const openModal = () => {
    setIsRouteListVisible(false);
    setSelectedRegion(null);
    setRoutes([]);
    setModalVisible(true);
  };

  const fetchRoutes = async (storeId: string) => {
    try {
      const response = await dispatch(fetchStoreRoutes({storeId})).unwrap();
      console.log(response, 'respdddonces');
      const routeOptions = response?.data?.map(
        (route: {name: string; id: string}) => ({
          label: route.name,
          value: route.id,
        }),
      );
      setRoutes(routeOptions);
    } catch (error) {
      console.error('Failed to fetch routes', error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          touched && error ? styles.errorBorder : null,
        ]}
        onPress={openModal}>
        <Text
          style={[
            styles.selectedText,
            !selectedOption && styles.placeholderText,
          ]}>
          {selectedOption ? selectedOption.label : 'Select an option'}
        </Text>
        <Image source={imagePath.down_arrow} style={styles.arrowIcon} />
      </TouchableOpacity>
      {touched && error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}>
          <TouchableOpacity activeOpacity={1} style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label}</Text>
              <TouchableOpacity
                onPress={() => {
                  if (isRouteListVisible) {
                    setIsRouteListVisible(false);
                    setRoutes([]);
                    setSelectedRegion(null);
                  } else {
                    setModalVisible(false);
                  }
                }}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            {!isRouteListVisible && (
              <FlatList
                data={options}
                keyExtractor={item => item.value}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={styles.optionItem}
                    onPress={() => {
                      setSelectedRegion(item);
                      fetchRoutes(item.value);
                      setIsRouteListVisible(true);
                    }}>
                    <Text style={styles.optionText}>{item.label}</Text>
                  </TouchableOpacity>
                )}
              />
            )}

            {isRouteListVisible && (
              <FlatList
                data={routes}
                keyExtractor={item => item.value}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={styles.optionItem}
                    onPress={() => {
                      onSelect(item.value, selectedRegion?.value);
                      setModalVisible(false);
                      setIsRouteListVisible(false);
                      setSelectedRegion(null);
                      setRoutes([]);
                    }}>
                    <Text style={styles.optionText}>{item.label}</Text>
                  </TouchableOpacity>
                )}
              />
            )}
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: FontFamily.REGULAR,
    color: Colors.dark,
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    height: 48,
  },
  errorBorder: {
    borderColor: Colors.danger,
  },
  selectedText: {
    fontSize: 16,
    color: Colors.dark,
  },
  placeholderText: {
    color: Colors.listSeconary,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.listSeconary,
  },
  errorText: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: Colors.primary,
  },
  modalTitle: {
    fontSize: 18,
    // fontWeight: 'bold',
    color: Colors.white,
    // fontSize: 14,
    fontFamily: FontFamily.REGULAR,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.white,
    fontWeight: 'bold',
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.greyBackground,
  },
  optionText: {
    fontSize: 16,
    color: Colors.dark,
  },
  checkIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.primary,
  },
});

export default Dropdown;
