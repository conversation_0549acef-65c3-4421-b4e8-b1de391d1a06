import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { ACTIVE_API_BASE_URL } from '../../constants/api';

// Create the async thunk for fetching all employees
export const fetchEmployeesApi = createAsyncThunk(
  'employeeApi/fetchEmployees',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${ACTIVE_API_BASE_URL}/Employee`
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Create the async thunk for fetching a single employee by ID
export const fetchEmployeeByIdApi = createAsyncThunk(
  'employeeApi/fetchEmployeeById',
  async (employeeId: string, { rejectWithValue }) => {
    try {
      console.log(employeeId, "emptidfjdf")
      const response = await axios.get(
        `${ACTIVE_API_BASE_URL}/Employee/${employeeId}`
      );
      console.log(response,ACTIVE_API_BASE_URL, "refdf")
      return response.data;
    } catch (error) {
      console.log(error,ACTIVE_API_BASE_URL, "eredjf")
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Create the async thunk for adding an employee
export const addEmployeeApi = createAsyncThunk(
  'employeeApi/addEmployee',
  async (employeeData: any, { rejectWithValue }) => {
    try {
      console.log(employeeData, "emptjskdfdf")
      const response = await axios.post(
        `${ACTIVE_API_BASE_URL}/Employee`,
        employeeData
      );
      console.log(response, "asjdhdsjfsh")
      return response.data;
    } catch (error) {
      console.log(error, "whielsdkjfd")
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);


