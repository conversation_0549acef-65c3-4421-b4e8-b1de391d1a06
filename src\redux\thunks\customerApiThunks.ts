// import { createAsyncThunk } from '@reduxjs/toolkit';
// import axios from 'axios';
// import { BASE_URL } from '../../constants/colors';

// // Create the async thunk for updating a customer
// export const updateCustomerApi = createAsyncThunk(
//   'customerApi/updateCustomer',
//   async ({ customerId, customerData }: { customerId: string, customerData: any }, { rejectWithValue }) => {
//     try {
//       const response = await axios.put(
//         `http://172.21.1.248:8080/api/v1/Customer${customerId}`,
//         customerData
//       );
//       return response.data;
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         return rejectWithValue(error.response?.data || error.message);
//       }
//       return rejectWithValue('An unknown error occurred');
//     }
//   }
// );

// // Create the async thunk for deleting a customer
// export const deleteCustomerApi = createAsyncThunk(
//   'customerApi/deleteCustomer',
//   async (customerId: string, { rejectWithValue }) => {
//     try {
//       await axios.delete(`http://172.21.1.248:8080/api/v1/Customer/${customerId}`);
//       return customerId; // Return the ID for the reducer to remove from state
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         return rejectWithValue(error.response?.data || error.message);
//       }
//       return rejectWithValue('An unknown error occurred');
//     }
//   }
// );
