import {Dimensions, ImageSourcePropType} from 'react-native';

export const {width, height} = Dimensions.get('window');

interface ImageListItem {
  image: ImageSourcePropType;
  caption: string;
}

export const imageList: ImageListItem[] = [
  {
    image: require('../assets/images/farm.png'),
    caption: 'Pure milk, straight from healthy cows to your doorstep.',
  },
  {
    image: require('../assets/images/society.png'),
    caption: 'Collected early morning and delivered fresh to 6 AM stores',
  },
  {
    image: require('../assets/images/customer.png'),
    caption: 'Delivered fresh to 6 AM stores – from farm to home.',
  },
  {
    image: require('../assets/images/customer.png'),
    caption: 'No middlemen, no compromise — just farm-fresh milk.',
  },
];

export const Colors = {
    primary: "#7CC467",
    secondary: "#6c757d",
    success: "#28a745",
    danger: "#dc3545",
    warning: "#ffc107",
    info: "#17a2b8",
    light: "#f8f9fa",
    dark: "#343a40",
    white: "#FFFFFF",
    black: "#000000",
    BackgroundGreen :"#C0F8B1",
    greyBackground :'#e0e0e0',
    borderColour:"#7CC467",
    greenColour : '#6C8F25',
    listSeconary :"#666",
    yellowColour : '#FFC107',
    orangeColour : '#FF9800',
    greyText : '#666',
    lightGreen : '#C0F8B1',
    StartBackground : "#FFBC00"
  };
  
  export const BASE_URL = 'http://43.205.200.131:8080'; 
  export const BASE_IMAGE_URL = 'http://43.205.200.131:8082'; 

  


//   34.45.149.30 - Google Cloud
// 43.204.142.13 - AWS
//13.203.105.93 - have increased it into 32 GB storage space.

//13.203.192.196 -  production server 

//43.205.200.131 - test server 