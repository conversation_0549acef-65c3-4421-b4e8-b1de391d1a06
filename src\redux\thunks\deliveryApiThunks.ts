import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { ACTIVE_API_BASE_URL } from '../../constants/api';

// Create the async thunk for fetching deliveries from API
export const fetchDeliveriesApi = createAsyncThunk(
  'deliveryApi/fetchDeliveries',
  async ({ userId, deliveryDate }: { userId?: string, deliveryDate: string|any }, { rejectWithValue }) => {
    try {
      // Format date correctly if needed
          if (!userId) throw new Error("Missing userId");

      const formattedDate = encodeURIComponent(deliveryDate);
      console.log(formattedDate, userId, "userId");
      console.log("deliveryDate ::",deliveryDate)
      const response = await axios.get(
        `${ACTIVE_API_BASE_URL}/Delivery/GetDeliveries/${userId}?deliveryDate=${deliveryDate}`
      );
      console.log(response, "sdfjkdsf")
      return response.data;
    } catch (error) {
      console.log(error, "ereidkf")
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Create the async thunk for adding a delivery
export const addDeliveryApi = createAsyncThunk(
  'deliveryApi/addDelivery',
  async (deliveryData: any, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${ACTIVE_API_BASE_URL}/Delivery`,
        deliveryData
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Create the async thunk for updating a delivery
export const updateDeliveryApi = createAsyncThunk(
  'deliveryApi/updateDelivery',
  async ({ deliveryId, deliveryData }: { deliveryId: string, deliveryData: any }, { rejectWithValue }) => {
    try {
      const response = await axios.put(
        `${ACTIVE_API_BASE_URL}/Delivery/${deliveryId}`,
        deliveryData
      );
      console.log(response, "eredjkf")
      return response.data;
    } catch (error) {
      console.log(error, "udpsadlkjfdsfer")
      if (axios.isAxiosError(error)) {  
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Create the async thunk for deleting a delivery
export const deleteDeliveryApi = createAsyncThunk(
  'deliveryApi/deleteDelivery',
  async (deliveryId: string, { rejectWithValue }) => {
    try {
      await axios.delete(`${ACTIVE_API_BASE_URL}/Delivery/${deliveryId}`);
      return deliveryId; // Return the ID for the reducer to remove from state
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Add update delivery status thunk
export const updateDeliveryStatus = createAsyncThunk(
  'deliveryApi/updateDeliveryStatus',
  async (
    {
      deliveryId,
      customerId,
      userId = 'ae94c59c-9065-427a-932e-3bf4f35665dd',
    }: {
      deliveryId: string | number;
      customerId?: string | number;
      userId?: string;
    },
    { rejectWithValue }
  ) => {
    const url = `${ACTIVE_API_BASE_URL}/Delivery/UpdateDeliveryStatus/${customerId}/${deliveryId}`;
    
    try {
      console.log('📤 Sending Get request to:', url);
      console.log('Customer ID:', customerId);

      const response = await axios.get(url);

      console.log('✅ API Response:', response.data);
      return response.data;

    } catch (error) {
      console.error('❌ Error while updating delivery status:', error);

      if (axios.isAxiosError(error)) {
        console.error('🔍 Axios error response:', error.response?.data);
        console.error('🔍 Axios error status:', error.response?.status);
        return rejectWithValue(error.response?.data || error.message);
      }

      return rejectWithValue('An unknown error occurred');
    }
  }
);
export const startDeliveryApi = createAsyncThunk(
  'deliveryApi/startDeliveryApi',
  async ({empId}:{empId:string}, { rejectWithValue }) => {
    try {
        console.log(empId, "emddpid")
      const response = await
     axios.put(
        `${ACTIVE_API_BASE_URL}/Delivery/StartDelivery/${empId}`
      );
      console.log(response, "responcefd")
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);