import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
} from 'react-native';

// Replace this with your actual image import
// import { imagePaths } from '../../constants/colors'; 
// Example: const callIcon = require('../../assets/call_icon.png');

interface SimSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (number: string | null) => void;
  firstNumber: string | null;
  secondNumber: string | null;
}

const SimSelectionModal: React.FC<SimSelectionModalProps> = ({
  visible,
  onClose,
  onSelect,
  firstNumber,
  secondNumber,
}) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <Text style={styles.title}>Continue with</Text>

              {firstNumber && (
                <TouchableOpacity
                  style={styles.option}
                  onPress={() => onSelect(firstNumber)}
                >
                  <View style={styles.iconCircle}>
                    <Image
                      source={require('../../assets/images/close.png')} // Replace with actual path
                      style={styles.icon}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.numberText}>{firstNumber}</Text>
                </TouchableOpacity>
              )}

              {secondNumber && (
                <TouchableOpacity
                  style={styles.option}
                  onPress={() => onSelect(secondNumber)}
                >
                  <View style={styles.iconCircle}>
                    <Image
                      source={require('../../assets/images/close.png')} // Replace with actual path
                      style={styles.icon}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.numberText}>{secondNumber}</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                onPress={() => onSelect(null)}
                style={styles.noneOption}
              >
                <Text style={styles.noneText}>NONE OF THE ABOVE</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default SimSelectionModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    width: '85%',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  title: {
    fontSize: 17,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 0.6,
    borderColor: '#ddd',
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F2F2',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: '#555',
  },
  numberText: {
    fontSize: 15,
    color: '#333',
  },
  noneOption: {
    marginTop: 20,
  },
  noneText: {
    color: '#1976D2',
    fontWeight: 'bold',
    fontSize: 14,
  },
});
