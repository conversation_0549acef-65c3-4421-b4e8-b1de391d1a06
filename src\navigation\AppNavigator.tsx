import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import CustomerDetailScreen from '../screens/CustomerDetailScreen';
import MapScreen from '../screens/MapScreen';
import DeliveryListScreen from '../screens/DeliveryListScreen';
import CustomerListScreen from '../screens/CustomerListScreen';
import PhoneInputScreen from '../screens/Auth/PhoneInputScreen';

export type RootStackParamList = {
  Map: undefined;
  CustomerList: undefined;
  CustomerDetail: { customerId?: string;customerData?:any; viewOnly?: boolean };
  DeliveryList: undefined; 
  Splash: undefined;
  PhoneNumber: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator = ({ initialRouteName = "Map" }): React.JSX.Element => {
  return (
    <Stack.Navigator
      initialRouteName={initialRouteName as keyof RootStackParamList}
      screenOptions={{
        headerShown: false,
      }}>
       <Stack.Screen name="PhoneNumber" component={PhoneInputScreen} />
      <Stack.Screen name="Map" component={MapScreen} />
      <Stack.Screen name="CustomerList" component={CustomerListScreen} />
      <Stack.Screen name="CustomerDetail" component={CustomerDetailScreen} />
      <Stack.Screen name="DeliveryList" component={DeliveryListScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
