import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {Colors} from '../../constants/colors';
import LottieView from 'lottie-react-native';
import {imagePath} from '../../constants/imagePath';
import {animationPath} from '../../constants/animationPath';

const SplashScreen = (): React.JSX.Element => {
  return (
    <View style={{...styles.container}}>
      <View
        style={{
          justifyContent: 'center',
          alignContent: 'center',
          position: 'relative',
          top: 100,
        }}>
        <LottieView
          source={animationPath.delivery_Icon}
          autoPlay
          loop={false}
          speed={0.4}
          style={styles.centerImage}
        />

        <Image source={imagePath.freshah_directah} style={[styles.textImage]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.greyBackground,
  },
  centerImage: {
    width: 165,
    height: 165,
    bottom: 10,
    resizeMode: 'contain',
  },
  textImage: {
    width: 175,
    height: 175,
    bottom: 10,
    resizeMode: 'contain',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBox: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    width: '80%',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  numberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  numberText: {
    fontSize: 16,
    marginRight: 10,
  },
  callCircle: {
    backgroundColor: '#1DBF73',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    marginTop: 20,
    paddingVertical: 8,
    paddingHorizontal: 20,
    backgroundColor: '#FE724E',
    borderRadius: 12,
  },
  closeText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default SplashScreen;
