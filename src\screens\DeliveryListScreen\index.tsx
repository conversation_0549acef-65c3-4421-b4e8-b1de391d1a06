import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Colors } from '../../constants/colors';
import { imagePath } from '../../constants/imagePath';
import DeleteConfirmationModal from '../../components/DeleteConfirmationModal';
import DeliveryListHeader from './Header';
import DeliveryList from './DeliveryList';

const DeliveryListScreen = (): React.JSX.Element => {
  const navigation = useNavigation();
  const route = useRoute();
  const deliveriesFromNav = route.params?.deliveries || [];
  
  console.log('Deliveries received from navigation:', deliveriesFromNav);

  const [deliveries, setDeliveries] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deliveryToDelete, setDeliveryToDelete] = useState<string | null>(null);

  // Use the deliveries from navigation if available
  React.useEffect(() => {
    if (deliveriesFromNav && Array.isArray(deliveriesFromNav)) {
      const formattedDeliveries = deliveriesFromNav.map(delivery => ({
        id: delivery.deliveryId,
        customerName: delivery.customerDetail.name,
        status: delivery.isDelivered ? 'Delivered' : 'Pending',
        address: `${delivery.customerDetail.houseType}, ${delivery.customerDetail.doorNumber}`,
        date: new Date().toISOString().split('T')[0],
        products: delivery.products.map((product: { description: string; quantity: number; unitType: string }) => ({
          name: product.description,
          quantity: product.quantity,
          unitType: product.unitType
        }))
      }));
      
      setDeliveries(formattedDeliveries);
      console.log('Formatted deliveries:', formattedDeliveries);
    } else {
    }
  }, [deliveriesFromNav]);

  const handleAddDelivery = () => {
    // Navigate to delivery detail screen
    // navigation.navigate('DeliveryDetail' as never);
  };

  const handleSelectDelivery = (delivery: any) => {
    // Navigate to delivery detail screen with data
    // navigation.navigate('DeliveryDetail', { deliveryId: delivery.id, deliveryData: delivery } as never);
  };

  const handleEditDelivery = (deliveryId: string) => {
    // Navigate to edit delivery screen
    // navigation.navigate('DeliveryDetail', { deliveryId } as never);
  };

  const handleDeleteDelivery = (deliveryId: string) => {
    setDeliveryToDelete(deliveryId);
    setIsDeleteModalVisible(true);
  };

  const confirmDelete = () => {
    if (deliveryToDelete) {
      const updatedDeliveries = deliveries.filter(
        delivery => delivery.id !== deliveryToDelete
      );
      setDeliveries(updatedDeliveries);
    }
    setIsDeleteModalVisible(false);
    setDeliveryToDelete(null);
  };

  const cancelDelete = () => {
    setIsDeleteModalVisible(false);
    setDeliveryToDelete(null);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const filteredDeliveries = searchQuery
    ? deliveries.filter(
        delivery =>
          delivery.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          delivery.address.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : deliveries;

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <DeliveryListHeader
        title="Delivery List"
        onAddPress={handleAddDelivery}
        onSearch={handleSearch}
      />

      <DeliveryList
        deliveries={filteredDeliveries}
        onSelectDelivery={handleSelectDelivery}
        onEditDelivery={handleEditDelivery}
        onDeleteDelivery={handleDeleteDelivery}
      />


      <DeleteConfirmationModal
        visible={isDeleteModalVisible}
        onCancel={cancelDelete}
        onConfirm={confirmDelete}
        title="Delete Delivery"
        message="Are you sure you want to delete this delivery?"
      />
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  fabButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    backgroundColor: Colors.greenColour,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
  },
  fabIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
});

export default DeliveryListScreen;
