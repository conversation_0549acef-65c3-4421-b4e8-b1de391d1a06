import React, { useState, useEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from './src/redux/store';
import { NavigationContainer } from '@react-navigation/native';
import AppNavigator from './src/navigation/AppNavigator';
import { ApiProvider } from './src/context/ApiContext';
import SplashScreen from './src/screens/SplashScreen';
import { StatusBar, StyleSheet } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LocationChecker from './src/screens/Auth/LocationChecker';

function App(): React.JSX.Element {
  const [isLoading, setIsLoading] = useState(true);
  const [initialRoute, setInitialRoute] = useState<string>('PhoneNumber'); // Default route

  useEffect(() => {
    const loadApp = async () => {
      try {
        const storedData = await AsyncStorage.getItem('employeeData');
        if (storedData) {
          setInitialRoute('Map');
        } else {
          setInitialRoute('PhoneNumber');
        }
      } catch (error) {
        console.error('Failed to load employeeData:', error);
        setInitialRoute('PhoneNumber');
      } finally {
        setTimeout(() => {
          setIsLoading(false);
        }, 1500); // shorter splash screen delay
      }
    };

    loadApp();
  }, []);

  if (isLoading) {
    return <SplashScreen />;
  }

  return (
    <Provider store={store}>
      <ApiProvider>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
        <NavigationContainer>
          <AppNavigator initialRouteName={initialRoute} />
        </NavigationContainer>
        <LocationChecker />
      </ApiProvider>
    </Provider>
  );
}

export default App;
