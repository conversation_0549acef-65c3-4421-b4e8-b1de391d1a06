import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {Colors} from '../../../constants/colors';
import {imagePath} from '../../../constants/imagePath';
import {useNavigation} from '@react-navigation/native';
import { FontFamily } from '../../../constants/fontFamily';

interface HeaderProps {
  title: string;
}

const Header = ({title}: HeaderProps): React.JSX.Element => {
  const navigation = useNavigation();

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
          <Image source={imagePath.back_arrow_icon} style={styles.icon} />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.rightContainer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  leftContainer: {
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  
  },
  rightContainer: {
    width: 40,
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
    textAlign: 'center',
    fontFamily: FontFamily.REGULAR,
  },
  iconButton: {
    padding: 8,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
});

export default Header;