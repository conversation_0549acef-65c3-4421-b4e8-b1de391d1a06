import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  Image,
} from 'react-native';
import { Colors } from '../../constants/colors';
import { useAppSelector, useAppDispatch } from '../../redux/hooks';
import { imagePath } from '../../constants/imagePath';
import { fetchEmployees } from '../../redux/thunks/employeeThunks';

interface DeliveryBoyModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectDeliveryBoy: (employeeId: number) => void;
}

const DeliveryBoyModal = ({
  visible,
  onClose,
  onSelectDeliveryBoy,
}: DeliveryBoyModalProps): React.JSX.Element => {
  const dispatch = useAppDispatch();
  const { data: employees, status, error } = useAppSelector((state) => state.employees);
  const [searchQuery, setSearchQuery] = useState('');

  // useEffect(() => {
  //   if (visible && status === 'idle') {
  //     dispatch(fetchEmployees());
  //   }
  // }, [visible, status, dispatch]);

  // Filter delivery boys (employees with type "Delivery Boy")
  const deliveryBoys = employees.filter(
    employee => employee.employeeType.employeeTypeName === 'Delivery Person'
  );

  const filteredDeliveryBoys = deliveryBoys.filter(employee =>
    employee.employeeName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.employeeItem}
      onPress={() => onSelectDeliveryBoy(item.employeeId)}
    >
      <View style={styles.avatarContainer}>
        {item.profileImage ? (
          <Image 
            source={{ uri: item.profileImage }} 
            style={styles.avatar} 
            resizeMode="cover"
          />
        ) : (
          <Image 
            source={{uri:item.image}}
            style={styles.avatar} 
            resizeMode="contain"
          />
        )}
      </View>
      <View style={styles.employeeInfo}>
        <Text style={styles.employeeName}>{item.employeeName}</Text>
        {/* <Text style={styles.employeePhone}>{item.phoneNumber}</Text> */}
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Select Delivery Boy</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Image source={imagePath.search_Icon} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search delivery boy..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={Colors.listSeconary}
            />
          </View>

          {status === 'loading' ? (
            <ActivityIndicator size="large" color={Colors.primary} style={styles.loader} />
          ) : status === 'failed' ? (
            <Text style={styles.errorText}>Error: {error}</Text>
          ) : (
            <FlatList
              data={filteredDeliveryBoys}
              keyExtractor={(item) => item.employeeId.toString()}
              renderItem={renderItem}
              contentContainerStyle={styles.listContent}
              ListEmptyComponent={
                <Text style={styles.emptyText}>
                  {searchQuery ? 'No delivery boys found matching your search' : 'No delivery boys available'}
                </Text>
              }
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: Colors.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.white,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.greyBackground,
    margin: 10,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  searchIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.listSeconary
  },
  searchInput: {
    flex: 1,
    height: 40,
    paddingHorizontal: 10,
    color: Colors.dark,
  },
  listContent: {
    padding: 10,
  },
  employeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.greyBackground,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    backgroundColor: Colors.greyBackground,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.dark,
  },
  employeePhone: {
    fontSize: 14,
    color: Colors.listSeconary,
    marginTop: 4,
  },
  loader: {
    padding: 20,
  },
  errorText: {
    padding: 20,
    color: Colors.danger,
    textAlign: 'center',
  },
  emptyText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.listSeconary
  },
});

export default DeliveryBoyModal;