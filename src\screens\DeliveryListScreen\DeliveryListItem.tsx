import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Colors } from '../../constants/colors';
import { imagePath } from '../../constants/imagePath';

interface DeliveryListItemProps {
  item: any;
  onPress: (item: any) => void;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
}

const DeliveryListItem = ({
  item,
  onPress,
  onEdit,
  onDelete,
}: DeliveryListItemProps): React.JSX.Element => {
  
  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return Colors.greenColour;
      case 'in transit':
        return Colors.yellowColour || '#FFC107';
      case 'pending':
        return Colors.orangeColour || '#FF9800';
      default:
        return Colors.greyText;
    }
  };

  return (
    <TouchableOpacity
    disabled ={true}
      style={styles.deliveryItem}
      onPress={() => onPress(item)}>
      <View style={styles.leftContainer}>
        {item.customerImage ? (
          <Image 
            source={{ uri: item.customerImage }} 
            style={styles.customerImage} 
            resizeMode="cover"
          />
        ) : (
          <View style={styles.customerImagePlaceholder}>
            <Image 
              source={imagePath.person_Icon} 
              style={styles.placeholderIcon} 
              resizeMode="contain"
            />
          </View>
        )}
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <Text style={styles.customerName}>{item.customerName}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
        
        <View style={styles.detailsRow}>
          <Image source={imagePath.location_Icon} style={styles.icon} resizeMode='contain' />
          <Text style={styles.addressText}>{item.address}</Text>
        </View>
        
        <View style={styles.detailsRow}>
          <Image source={imagePath.calendar_Icon} style={styles.icon} />
          <Text style={styles.dateText}>{item.date}</Text>
        </View>
        
        {item.products && item.products.length > 0 && (
          <View style={styles.productInfo}>
            {item.products.map((product: { name: string; quantity: number; unitType: string }, index: number) => (
              <Text key={index} style={styles.productText}>
                {product.name}: {product.quantity} {product.unitType}
              </Text>
            ))}
          </View>
        )}
      </View>
      
      <View style={styles.actionsContainer}>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  deliveryItem: {
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  leftContainer: {
    marginRight: 12,
  },
  customerImage: {
    width: 85,
    height:85,
    borderRadius: 5,
    // borderRadius: 25,
    backgroundColor: Colors.greyBackground,
  },
  customerImagePlaceholder: {
    width: 85,
    height:85,
    borderRadius: 5,
    backgroundColor: Colors.greyBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.greyText,
  },
  contentContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.dark,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: Colors.white,
    fontWeight: 'bold',
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  icon: {
    width: 16,
    height: 16,
    marginRight: 8,
    tintColor: Colors.greyText,
  },
  addressText: {
    fontSize: 14,
    color: Colors.greyText,
    flex: 1,
  },
  dateText: {
    fontSize: 14,
    color: Colors.greyText,
  },
  actionsContainer: {
    justifyContent: 'center',
  },
  actionButton: {
    padding: 8,
  },
  actionIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.greyText,
  },
  productInfo: {
    marginTop: 4,
  },
  productText: {
    fontSize: 12,
    color: Colors.listSeconary,
    marginBottom: 2,
  },
});

export default DeliveryListItem;




