// src/utils/storage.ts

import AsyncStorage from '@react-native-async-storage/async-storage';

export const markOtpVerified = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem('isVerified', 'true');
    console.log('User marked as verified in AsyncStorage');
  } catch (error) {
    console.error('Error saving verification status:', error);
  }
};

export const storeSelectedRegion = async (regionId: string, regionName: string): Promise<void> => {
  try {
    const regionData = JSON.stringify({ id: regionId, name: regionName });
    await AsyncStorage.setItem('selectedRegion', regionData);
    console.log('Region stored successfully:', regionId, regionName);
  } catch (error) {
    console.error('Error storing region:', error);
  }
};

export const getSelectedRegion = async (): Promise<{ id: string; name: string; listings?: any[] } | null> => {
  try {
    const regionData = await AsyncStorage.getItem('selectedRegion');
    return regionData ? JSON.parse(regionData) : null;
  } catch (error) {
    console.error('Error retrieving region:', error);
    return null;
  }
};
export const storeEmployeeData = async (employeeData: any): Promise<void> => {
  try {
    await AsyncStorage.setItem('employeeData', JSON.stringify(employeeData));
    console.log('Employee data stored successfully');
  } catch (error) {
    console.error('Error storing employee data:', error);
  }
};
export const storeEmployeeStores = async (employeeStores: any[]) => {
  try {
    await AsyncStorage.setItem('employeeStores', JSON.stringify(employeeStores));
  } catch (error) {
    console.error('Failed to store employee stores:', error);
  }
};