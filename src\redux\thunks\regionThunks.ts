import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { BASE_URL } from '../../constants/colors';
import { ACTIVE_API_BASE_URL } from '../../constants/api';

// Create the async thunk for fetching regions
const Url=`${ACTIVE_API_BASE_URL}/Store`

export const fetchRegions = createAsyncThunk(
  'regions/fetchRegions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(Url);
      console.log(response, "rendjsdf")
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);
export const fetchRotes = createAsyncThunk(
  'regions/fetchRotes',
  async ({empId}:{empId:string}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${ACTIVE_API_BASE_URL}/Route/${empId}`);
      console.log(response, "rendjsdddf")
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);
export const fetchStoreRoutes = createAsyncThunk(
  'regions/fetchStoreRoutes',
  async ({storeId}:{storeId:string}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${ACTIVE_API_BASE_URL}/Route/store/${storeId}`);
      console.log(response, "rendjsdddf")
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);