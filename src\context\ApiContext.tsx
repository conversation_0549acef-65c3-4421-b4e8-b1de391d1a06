// import AsyncStorage from '@react-native-async-storage/async-storage';
// import axios from 'axios';
// import React, {createContext, useState, ReactNode} from 'react';
// import {Platform, PermissionsAndroid, ToastAndroid} from 'react-native';
// import { GOOGLE_API_KEY } from '../constants/apiKeys';

// interface ApiContextType {
//   api: {
//     requestLocationPermission: () => Promise<boolean>;
//     requestReadSmsPermission: () => Promise<boolean>;
//     requestSmsPermission: () => Promise<boolean>;
//     sendOtpSms: (mobileNumber: string) => Promise<any>;
//     getStoredPhoneNumber: () => Promise<string | null>;
//     isOtpVerified: boolean;
//     markOtpVerified: () => Promise<void>;
//     isLoading: boolean;
//     markAppLoaded: () => void;
//     requestCameraPermission: () => Promise<boolean>;
//     storeSelectedRegion: (regionId: string, regionName: string) => Promise<void>;
//     getSelectedRegion: () => Promise<{id: string, name: string, listings?: any[]} | null>;
//     showToast: (message: string) => void;
//       getAddressFromCoordinates: (
//     latitude: number,
//     longitude: number
//   ) => Promise<string | null>;
    
//   };
// }

// interface ApiProviderProps {
//   children: ReactNode;
// }

// const ApiContext = createContext<ApiContextType | undefined>(undefined);

// export const ApiProvider: React.FC<ApiProviderProps> = ({children}) => {
//   const [isOtpVerified, setIsOtpVerified] = useState<boolean>(false);
//   const [isLoading, setIsLoading] = useState<boolean>(true);

//   const markOtpVerified = async (): Promise<void> => {
//     try {
//       await AsyncStorage.setItem('isVerified', 'true');
//       setIsOtpVerified(true);
//       console.log('User marked as verified in AsyncStorage');
//     } catch (error) {
//       console.error('Error saving verification status:', error);
//     }
//   };

//   const markAppLoaded = (): void => setIsLoading(false);

//   const requestLocationPermission = async (): Promise<boolean> => {
//     try {
//       if (Platform.OS === 'android') {
//         const granted = await PermissionsAndroid.request(
//           PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
//           {
//             title: 'Location Permission',
//             message: 'This app needs access to your location.',
//             buttonNeutral: 'Ask Me Later',
//             buttonNegative: 'Cancel',
//             buttonPositive: 'OK',
//           },
//         );

//         if (granted === PermissionsAndroid.RESULTS.GRANTED) {
//           console.log('Location permission granted.');
//           return true;
//         } else {
//           console.log('Location permission denied.');
//           return false;
//         }
//       }
//       return false;
//     } catch (err) {
//       console.error('Failed to request location permission:', err);
//       return false;
//     }
//   };

//   const requestCameraPermission = async (): Promise<boolean> => {
//     try {
//       if (Platform.OS === 'android') {
//         const granted = await PermissionsAndroid.request(
//           PermissionsAndroid.PERMISSIONS.CAMERA,
//           {
//             title: 'Camera Permission',
//             message: 'This app needs access to your camera.',
//             buttonNeutral: 'Ask Me Later',
//             buttonNegative: 'Cancel',
//             buttonPositive: 'OK',
//           },
//         );

//         if (granted === PermissionsAndroid.RESULTS.GRANTED) {
//           console.log('Camera permission granted.');
//           return true;
//         } else {
//           console.log('Camera permission denied.');
//           return false;
//         }
//       }
//       return false;
//     } catch (err) {
//       console.error('Failed to request camera permission:', err);
//       return false;
//     }
//   };

//   // Add the new functions to store and retrieve region
//   const storeSelectedRegion = async (regionId: string, regionName: string): Promise<void> => {
//     console.log('Starting to store region:', regionId, regionName);
//     try {
//       const regionData = JSON.stringify({id: regionId, name: regionName});
//       await AsyncStorage.setItem('selectedRegion', regionData);
//       console.log('Region stored successfully:', regionId, regionName);
//     } catch (error) {
//       console.error('Error storing region:', error);
//     }
//   };

//   const getSelectedRegion = async (): Promise<{id: string, name: string, listings?: any[]} | null> => {
//     try {
//       const regionData = await AsyncStorage.getItem('selectedRegion');
//       if (regionData) {
//         return JSON.parse(regionData);
//       }
//       return null;
//     } catch (error) {
//       console.error('Error retrieving region:', error);
//       return null;
//     }
//   };

//   const showToast = (message: string) => {
//     ToastAndroid.show(message, ToastAndroid .SHORT);
//   };

//  const getAddressFromCoordinates = async (
//   latitude: number,
//   longitude: number
// ): Promise<string | null> => {

//   try {
//     const response = await axios.get<{
//       results: { formatted_address: string }[];
//       status: string;
//     }>(
//       `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_API_KEY}`
//     );

//     if (response.data.status === 'OK' && response.data.results.length > 0) {
//       return response.data.results[0].formatted_address;
//     } else {
//       console.error('Geocoding error:', response.data.status);
//       return null;
//     }
//   } catch (error: any) {
//     console.error('Geocoding API error:', error.message || error);
//     return null;
//   }
// };

//   const api = {
//     requestLocationPermission,
//     isOtpVerified,
//     markOtpVerified,
//     isLoading,
//     markAppLoaded,
//     requestCameraPermission,
//     storeSelectedRegion,
//     getSelectedRegion,
//     showToast,
//     getAddressFromCoordinates
//   };

//   return <ApiContext.Provider value={{
//     api: {
//       ...api,
//       requestReadSmsPermission: async () => false,
//       requestSmsPermission: async () => false,
//       sendOtpSms: async (mobileNumber: string) => null,
//       getStoredPhoneNumber: async () => null
//     }
//   }}>{children}</ApiContext.Provider>;
// };

// export default ApiContext;



import React, { createContext, useState, ReactNode } from 'react';
import { ToastAndroid } from 'react-native';
import {
  requestCameraPermission,
  requestLocationPermission
} from '../utils/permissions';
import {
  markOtpVerified as saveOtpVerified,
  storeSelectedRegion,
  getSelectedRegion
} from '../utils/storage';
import { getAddressFromCoordinates } from '../utils/location';

interface ApiContextType {
  api: {
    requestLocationPermission: () => Promise<boolean>;
    requestCameraPermission: () => Promise<boolean>;
    requestReadSmsPermission: () => Promise<boolean>;
    requestSmsPermission: () => Promise<boolean>;
    sendOtpSms: (mobileNumber: string) => Promise<any>;
    getStoredPhoneNumber: () => Promise<string | null>;
    isOtpVerified: boolean;
    markOtpVerified: () => Promise<void>;
    isLoading: boolean;
    markAppLoaded: () => void;
    storeSelectedRegion: (regionId: string, regionName: string) => Promise<void>;
    getSelectedRegion: () => Promise<{ id: string; name: string; listings?: any[] } | null>;
    getAddressFromCoordinates: (latitude: number, longitude: number) => Promise<string | null>;
    showToast: (message: string) => void;
  };
}

interface ApiProviderProps {
  children: ReactNode;
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const [isOtpVerified, setIsOtpVerified] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const markOtpVerified = async (): Promise<void> => {
    await saveOtpVerified();
    setIsOtpVerified(true);
  };

  const markAppLoaded = (): void => setIsLoading(false);

  const showToast = (message: string): void => {
    ToastAndroid.show(message, ToastAndroid.SHORT);
  };

  const api = {
    requestLocationPermission,
    requestCameraPermission,
    storeSelectedRegion,
    getSelectedRegion,
    getAddressFromCoordinates,
    markOtpVerified,
    markAppLoaded,
    showToast,
    isOtpVerified,
    isLoading,
    // Placeholder methods for expansion
    requestReadSmsPermission: async () => false,
    requestSmsPermission: async () => false,
    sendOtpSms: async (_: string) => null,
    getStoredPhoneNumber: async () => null
  };

  return <ApiContext.Provider value={{ api }}>{children}</ApiContext.Provider>;
};

export default ApiContext;



