// import { createAsyncThunk } from '@reduxjs/toolkit';
// import axios from 'axios';
// import { BASE_URL } from '../../constants/colors';

// // Create the async thunk for adding a customer via API
// export const addCustomerApi = createAsyncThunk(
//   'customerApi/addCustomer',
//   async (customerData: any, { rejectWithValue }) => {
//     try {
//       const response = await axios.post(
//         `http://172.21.1.248:8080/api/v1/Customer`,
//         customerData
//       );
//       return response.data;
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         return rejectWithValue(error.response?.data || error.message);
//       }
//       return rejectWithValue('An unknown error occurred');
//     }
//   }
// );