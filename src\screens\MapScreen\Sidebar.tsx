import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
} from 'react-native';
import {Colors} from '../../constants/colors';
import {imagePath} from '../../constants/imagePath';
import {APP_VERSION, APP_NAME} from '../../constants/appConfig';

interface SidebarProps {
  visible: boolean;
  onClose: () => void;
  onDeliveryBoyPress: () => void;
  onCustomerListPress: () => void;
  onDeliveryListPress: () => void;
  onChooseRegionPress: () => void;
  hasStarted?: boolean;
  onPhoneInputPress: () => void; 
}

const {width} = Dimensions.get('window');

const Sidebar = ({
  visible,
  onClose,
  onDeliveryBoyPress,
  onCustomerListPress,
  onDeliveryListPress,
  onChooseRegionPress,
  hasStarted,
  onPhoneInputPress, 
}: SidebarProps): React.JSX.Element | null => {
  
  return (
    
  visible ? (
    <View style={styles.container}>
      <View style={styles.sidebar}>
        <View style={styles.header}>
          <Image source={imagePath.app_logo_icon} style={styles.logo} resizeMode='contain'/>
          <Text style={styles.headerTitle}>{APP_NAME}</Text>
        </View>
        
        <ScrollView style={styles.menuContainer}>
          {!hasStarted && (
            <>
              <TouchableOpacity 
                style={styles.menuItem}
                onPress={onChooseRegionPress}
              >
                <Image source={imagePath.locate_Icon} style={styles.menuIcon} />
                <Text style={styles.menuText}>Region</Text>
              </TouchableOpacity>
              
              <View style={styles.divider} />
            </>
          )}
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={onCustomerListPress}
          >
            <Image source={imagePath.person_Icon} style={styles.menuIcon} />
            <Text style={styles.menuText}>Customer</Text>
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={onDeliveryListPress}
          >
            <Image source={imagePath.delivery_man_ICon} style={{...styles.menuIcon, height :28, width:28}} />
            <Text style={styles.menuText}>Current Delivery</Text>
          </TouchableOpacity>

          <View style={styles.divider} />

          {/* <TouchableOpacity 
            style={styles.menuItem}
            onPress={onPhoneInputPress}
          >
            <Image source={imagePath.person_Icon || imagePath.person_Icon} style={styles.menuIcon} />
            <Text style={styles.menuText}>Phone Input</Text>
          </TouchableOpacity> */}

          <View style={styles.divider} />
        </ScrollView>
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>Version {APP_VERSION}</Text>
        </View>
      </View>
      <TouchableOpacity style={styles.overlay} onPress={onClose} />
    </View>
  ) : null);
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    zIndex: 999,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sidebar: {
    width: width * 0.60,
    maxWidth: 300,
    backgroundColor: Colors.white,
    shadowColor: '#000',
    shadowOffset: {width: 2, height: 0},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 10,
  },
  header: {
    height: 120,
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  logo: {
    width: 40,
    height: 40,
  },
  headerTitle: {
    flex: 1,
    color: Colors.white,
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  closeButton: {
    padding: 8,
  },
  closeIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.white,
  },
  menuContainer: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  menuIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.primary,
  },
  menuText: {
    fontSize: 16,
    color: Colors.dark,
    marginLeft: 20,
    flex: 1,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.primary,
  },
  arrowUp: {
    transform: [{ rotate: '180deg' }],
  },
  divider: {
    height: 1,
    backgroundColor: Colors.greyBackground,
    marginHorizontal: 20,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.greyBackground,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: Colors.dark,
    opacity: 0.7,
  },
  languageOptions: {
    paddingLeft: 64,
    paddingRight: 20,
    backgroundColor: Colors.greyBackground,
  },
  languageOption: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  selectedLanguage: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  languageText: {
    fontSize: 14,
    color: Colors.dark,
  },
  selectedLanguageText: {
    fontWeight: 'bold',
    color: Colors.primary,
  },
});

export default Sidebar;