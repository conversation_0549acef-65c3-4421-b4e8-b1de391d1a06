import {createSlice} from '@reduxjs/toolkit';
import {fetchRegions, fetchRotes, fetchStoreRoutes} from '../thunks/regionThunks';
import {RootState} from '../store';

// Define a type for the region
interface Region {
  id: any;
  storeId: string;
  name: string;
  phone: number;
  address1: string;
  address2: string | null;
  cityId: string;
  stateId: string;
  countryId: string;
  postalCodeId: string;
  longitude: string;
  lattitude: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface Welcome {
  data: Datum[];
}

export interface Datum {
  routeId: string;
  routeName: string;
  storeDetail: StoreDetail;
  employeeId: string;
  routeSequences: RouteSequence[];
}

export interface RouteSequence {
  routeSequenceId: string;
  customerDetail: CustomerDetail;
  sequence: number;
}

export interface CustomerDetail {
  customerId: string;
  name: string;
  mobile: number;
  address1: string;
  address2: null;
  cityId: null;
  stateId: null;
  countryId: null;
  postalCodeId: null;
  city: null;
  state: null;
  country: null;
  postalCode: null;
  longitude: string;
  lattitude: string;
  landmark: null;
  email: string;
  doorNumber: string;
  floorNumber: string;
  houseType: string;
  region: string;
}

export interface StoreDetail {
  storeId: string;
  storeName: string;
}
export interface StoreRoute {
    id:   string;
    name: string;
}

interface RegionsState {
  data: Region[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  fetchLoad: 'idle' | 'loading' | 'succeeded' | 'failed';
  fetchLoad1: 'idle' | 'loading' | 'succeeded' | 'failed';
  routeData: Datum[];
  fetchStoreRouteLoad:'idle' | 'loading' | 'succeeded' | 'failed';
  StoreRouteData:StoreRoute[]
}

const initialState: RegionsState = {
  data: [],
  status: 'idle',
  error: null,
  fetchLoad: 'idle',
  fetchLoad1: 'idle',
  routeData: [],
  StoreRouteData:[],
  fetchStoreRouteLoad:"idle"
};

const regionsSlice = createSlice({
  name: 'regions',
  initialState,
  reducers: {
    resetRegionApiStatus: state => {
      state.fetchLoad = 'idle';
      state.fetchLoad1 = 'idle';
      state.status = 'idle';
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchRegions.pending, state => {
        state.fetchLoad1 = 'loading';
        state.status = 'loading';
      })
      .addCase(fetchRegions.fulfilled, (state, action) => {
        console.log(action.payload, 'actionpayload');
        state.status = 'succeeded';
        state.fetchLoad1 = 'succeeded';
        state.data = action.payload.data;
      })
      .addCase(fetchRegions.rejected, (state, action) => {
        state.status = 'failed';
        state.fetchLoad1 = 'failed';
        state.error = action.payload as string;
      });
    builder
      .addCase(fetchRotes.pending, state => {
        state.fetchLoad = 'loading';
      })
      .addCase(fetchRotes.fulfilled, (state, action) => {
        console.log(action.payload, 'actionpayload');
        state.fetchLoad = 'succeeded';
        state.routeData = action.payload.data;
      })
      .addCase(fetchRotes.rejected, (state, action) => {
        state.fetchLoad = 'failed';
        state.error = action.payload as string;
      });
         builder
      .addCase(fetchStoreRoutes.pending, state => {
        state.fetchStoreRouteLoad = 'loading';
      })
      .addCase(fetchStoreRoutes.fulfilled, (state, action) => {
        console.log(action.payload, 'actionpayload');
        state.fetchStoreRouteLoad = 'succeeded';
        state.StoreRouteData = action.payload.data;
      })
      .addCase(fetchStoreRoutes.rejected, (state, action) => {
        state.fetchStoreRouteLoad = 'failed';
        state.error = action.payload as string;
      });
  },
});
export const {resetRegionApiStatus} = regionsSlice.actions;

export const regionSelector = (state: RootState) => state.regions;

export default regionsSlice.reducer;
