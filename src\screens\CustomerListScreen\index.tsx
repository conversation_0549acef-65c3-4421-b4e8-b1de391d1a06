import React, {useState, useEffect, useContext} from 'react';
import {StyleSheet, TouchableOpacity, Image, Alert} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import Header from './CustomerListHeader';
import CustomerList from './CustomerList';
import {imagePath} from '../../constants/imagePath';
import {Colors} from '../../constants/colors';
import {SafeAreaView} from 'react-native-safe-area-context';
import {fetchCustomersApi} from '../../redux/thunks/Create/CustomerApiThunks';
import axios from 'axios';
import {ACTIVE_API_BASE_URL} from '../../constants/api';
import {
  customerSelector,
  resetCustomerApiStatus,
  resetCustomerData,
} from '../../redux/slices/Create/customerApiSlice';
import ApiContext from '../../context/ApiContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {fetchRotes} from '../../redux/thunks/regionThunks';
import {
  regionSelector,
  resetRegionApiStatus,
} from '../../redux/slices/regionsSlice';
import { AppDispatch } from '../../redux/store';

export interface CustomerImage {
  customerImageId: string;
  customerId: string;
  imageName: string;
  imageType: string | null;
  imageContent: string;
  createdBy: string;
  createdDate: string;
  updatedBy: string | null;
  updatedDate: string | null;
}

export interface CustomerProduct {
  customerProductId: string;
  customerId: string;
  productId: string;
  quantity: number;
  startDate: string;
  endDate: string;
  createdBy: string;
  createdDate: string;
  updatedBy: string | null;
  updatedDate: string | null;
}

export interface CustomerRouteDetail {
  routeId: string;
  routeName: string;
  storeId: string;
  storeName: string;
  routeSequenceId: string;
  sequence: number;
}

export interface Customer {
  customerCode: string;
  customerId: string;
  name: string;
  mobile: string;
  imageUrl?: string;
  housetype: string;
  region: string;
  floorNumber: string;
  cityId: string;
  stateId: string;
  countryId: string;
  postalCodeId: string;
  longitude: string;
  lattitude: string;
  landmark: string;
  email: string;
  doorNumber: string;
  images: CustomerImage[];
  customerProducts: CustomerProduct[];
  createdBy: string;
  createdDate: string;
  updatedBy: string | null;
  updatedDate: string | null;
  customerRouteDetail: CustomerRouteDetail;

  status: string | null;
}

function isSameOrder({list1, list2}: {list1: Customer[]; list2: Customer[]}) {
  if (list1.length !== list2.length) {
    return false;
  }
  return list1.every((item, index) => {
    const otherItem = list2[index];
    return item.customerId === otherItem.customerId;
  });
}

const ROUTE_ID = '142ccc0a-a9fa-4bea-9b3d-ebefc7ef0b94';
const EMPLOYEE_ID = 'ae94c59c-9065-427a-932e-3bf4f35665dd';
const ROUTE_NAME = 'Saravanampatti to Thudiyalur - Updated again updated';

const CustomerListScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  // const {data: customers, status} = useAppSelector(state => state.customerApi);
  const {api} = useContext(ApiContext) || {};

  const [search, setSearch] = useState<string>('');
  const {
    currentOperation,
    error,
    operationStatus,
    data: customers,
  } = useAppSelector(customerSelector);
  const {routeData, fetchLoad} = useAppSelector(regionSelector);
  interface EmptData {
    employeeId: string;
    name: string;
    mobile: number;
    address1: string;
    address2: string;
    cityId: string;
    stateId: string;
    countryId: string;
    postalCodeId: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    employeeStores: EmployeeStore[];
    employeeRoutes: EmployeeRoutes[];
  }

  interface EmployeeStore {
    storeId: string;
    storeName: string;
  }
  interface EmployeeRoutes {
    id: string;
    name: string;
  }
  const [showSearchInput, setShowSearchInput] = useState(false);
  const [customerList, setCustomerList] = useState<Customer[]>([]);
  const [isDragEnabled, setIsDragEnabled] = useState<boolean>(false);
  const [storedRegion, setStoredRegion] = React.useState(null);
  const [empData, setempData] = useState<EmptData>();
  console.log(empData, routeData, fetchLoad, 'sjedjkfdf');
  console.log(currentOperation, operationStatus, customers, 'ksdkdfkj');
  console.log(currentOperation,operationStatus, 'sjedjdkfdf1');
  const handleUpdateSequence = async () => {
    console.log(empData,currentOperation, 'sjedjdkfdf');
    const data = {
      routeId: empData?.employeeRoutes[0]?.id,
      routeName: empData?.employeeRoutes[0]?.name,
      employeeId: empData?.employeeId,
      routeSequences: customerList.map((model, index) => {
        return {
          customerId: model.customerId,
          routeSequenceId: model?.customerRouteDetail?.routeSequenceId,
          sequence: index + 1,
        };
      }),
    };
    console.log('Request12', data);

    try {
      const response = await axios.put(`${ACTIVE_API_BASE_URL}/Route`, data);
      setIsDragEnabled(false);
      setShowSearchInput(false);
      setSearch('');
      // Alert.alert(
      //   'Customer sequence updated',
      //   'Your customer sequence updated successfully',
      //   [

      //     // {
      //     //   text: 'Cancel',
      //     //   onPress: () => console.log('Cancel Pressed'),
      //     //   style: 'cancel',
      //     // },
      //     {text: 'OK', onPress: () => dispatch(fetchCustomersApi())},
      //   ],
      // );
      api?.showToast('customer sequence updated successfully');
      dispatch(fetchCustomersApi());
      console.log('Update successful:', response.data);
    } catch (error: any) {
      console.error(
        'Error updating route:',
        error.response?.data || error.message,
      );
    }
  };
  const getStoredEmployeeData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('employeeData');
      if (storedData) {
        const data = JSON.parse(storedData);
        console.log(data, 'Parsed employee data');
        setempData(data);
        return data;
      } else {
        console.log('No data found');
      }
    } catch (error) {
      console.error('Error reading employee data:', error);
    }
  };
  useEffect(() => {
    getStoredEmployeeData();
  }, []);
  useEffect(() => {
    dispatch(fetchCustomersApi());
  }, [dispatch]);
  const refreshCustomerData = () => async (dispatch: AppDispatch) => {
  await dispatch(resetCustomerData());
  await dispatch(fetchCustomersApi());
};

  useEffect(() => {
    if (
      operationStatus.update === 'succeeded' ||
      operationStatus.add === 'succeeded' ||
      operationStatus.imagesave === 'succeeded' ||
      operationStatus.imagesave === 'failed'
    ) {
      console.log('dispatchedHere');
    dispatch(refreshCustomerData());

    }
  }, [operationStatus.update, operationStatus.add, operationStatus.imagesave]);

  useEffect(() => {
    console.log('Fetch operation status:', operationStatus.fetch);
    if (operationStatus.fetch === 'succeeded') {
      console.log('Customer data fetched:', customers);
      setCustomerList(customers as Customer[]);

dispatch(resetCustomerApiStatus());
    }
  }, [operationStatus.fetch]);

  React.useEffect(() => {
    const fetchRegion = async () => {
      const region = await api?.getSelectedRegion();
      console.log('Fetched stored region:', region);
      if (region) {
        setStoredRegion(region as any);
      }
    };

    fetchRegion();
  }, []);

  const trimmedQuery = search.trim().toLowerCase();
  console.log('Search query:', trimmedQuery);

  // const filteredCustomers = React.useMemo(() => {
  //   console.log('Filtering customers with query:', trimmedQuery);
  //   const result = customers?.filter(({name, mobile, email}) => {
  //     const isMatch =
  //       name?.toLowerCase().includes(trimmedQuery) ||
  //       mobile?.toString().includes(trimmedQuery) ||
  //       email?.toLowerCase().includes(trimmedQuery);
  //     if (isMatch) {
  //       console.log('Matched customer:', {name, mobile, email});
  //     }
  //     return isMatch;
  //   });
  //   console.log('Filtered customers:', result);
  //   return result;
  // }, [customers, trimmedQuery]);
  const filteredCustomers = React.useMemo(() => {
    if (!empData?.employeeRoutes[0]?.id) return [];

    const regionFiltered = customerList?.filter(customer => {
      return (
        customer.customerRouteDetail?.routeId === empData?.employeeRoutes[0]?.id
      );
    });

    if (!trimmedQuery) return regionFiltered;

    return regionFiltered.filter(({name, mobile, email}) => {
      const lowerQuery = trimmedQuery.toLowerCase();
      return (
        name?.toLowerCase().includes(lowerQuery) ||
        mobile?.toString().includes(lowerQuery) ||
        email?.toLowerCase().includes(lowerQuery)
      );
    });
  }, [customerList, empData, trimmedQuery]);

  const handleAddCustomer = (): void => {
    navigation.navigate('CustomerDetail' as never);
    dispatch(resetCustomerApiStatus());
  };

  const handleEditCustomer = (customer: Customer): void => {
    navigation.navigate(
      'CustomerDetail' as never,
      {
        customerId: customer.customerId,
        customerData: customer,
      } as never,
    );
  };

  const handleSearch = React.useCallback((query: string): void => {
    setSearch(query);
  }, []);
  useEffect(() => {
    if (empData?.employeeId) {
      dispatch(fetchRotes({empId: empData.employeeId}));
    }
  }, [empData]);
  useEffect(() => {
    if (fetchLoad === 'succeeded') {
      dispatch(resetRegionApiStatus());
    }
  }, [fetchLoad]);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Customer List"
        setSearch={handleSearch}
        isDragEnabled={isDragEnabled}
        onSave={handleUpdateSequence}
        search={search}
        setShowSearchInput={setShowSearchInput}
        showSearchInput={showSearchInput}
      />

      <CustomerList
        customers={filteredCustomers}
        onEditCustomer={handleEditCustomer}
        isLoading={operationStatus.fetch === 'loading'}
        onUpdateCustomerList={s => {
          setIsDragEnabled(!isSameOrder({list1: s, list2: customers}));
          setCustomerList(s);
        }}
        enableDrag={!(trimmedQuery.length > 0)}
      />

      <TouchableOpacity style={styles.fabButton} onPress={handleAddCustomer}>
        <Image source={imagePath.plus_Icon} style={styles.fabIcon} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  fabButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: Colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  fabIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
});

export default CustomerListScreen;
