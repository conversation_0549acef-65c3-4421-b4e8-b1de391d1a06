// src/utils/location.ts

import axios from 'axios';
import { GOOGLE_API_KEY } from '../constants/apiKeys';

export const getAddressFromCoordinates = async (
  latitude: number,
  longitude: number
): Promise<string | null> => {
  try {
    const response = await axios.get<{
      results: { formatted_address: string }[];
      status: string;
    }>(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_API_KEY}`
    );

    if (response.data.status === 'OK' && response.data.results.length > 0) {
      return response.data.results[0].formatted_address;
    } else {
      console.error('Geocoding error:', response.data.status);
      return null;
    }
  } catch (error: any) {
    console.error('Geocoding API error:', error.message || error);
    return null;
  }
};
