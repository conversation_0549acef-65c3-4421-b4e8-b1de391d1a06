import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, TextInput } from 'react-native';
import { Colors } from '../../constants/colors';
import { imagePath } from '../../constants/imagePath';
import { useNavigation } from '@react-navigation/native';

interface DeliveryListHeaderProps {
  title: string;
  onAddPress: () => void;
  onSearch?: (query: string) => void;
}

const DeliveryListHeader = ({
  title,
  onAddPress,
  onSearch,
}: DeliveryListHeaderProps): React.JSX.Element => {
  const navigation = useNavigation();
  const [showSearchInput, setShowSearchInput] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleBackPress = () => {
    navigation.goBack();
  };

  const toggleSearchInput = () => {
    setShowSearchInput(!showSearchInput);
    if (showSearchInput) {
      setSearchQuery('');
      onSearch?.('');
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    onSearch?.(text);
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
          <Image source={imagePath.back_arrow_icon} style={styles.icon} />
        </TouchableOpacity>
      </View>
      
      {showSearchInput ? (
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search deliveries..."
            value={searchQuery}
            onChangeText={handleSearch}
            autoFocus
            placeholderTextColor={Colors.white}
          />
        </View>
      ) : (
        <Text style={styles.title}>{title}</Text>
      )}
      
      <View style={styles.rightContainer}>
        <TouchableOpacity onPress={toggleSearchInput} style={styles.iconButton}>
          <Image source={imagePath.search_Icon} style={styles.icon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.primary,
    height: 60,
    paddingHorizontal: 16,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
    flex: 1,
    textAlign: 'center',
  },
  iconButton: {
    padding: 8,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
  searchContainer: {
    flex: 1,
    marginHorizontal: 8,
  },
  searchInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    color: Colors.white,
  },
});

export default DeliveryListHeader;