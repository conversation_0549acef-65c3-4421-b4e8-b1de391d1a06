import {createSlice} from '@reduxjs/toolkit';
import {
  fetchCustomersApi,
  addCustomerApi,
  updateCustomerApi,
  deleteCustomerApi,
  uploadCustomerImagesApi,
} from '../../thunks/Create/CustomerApiThunks';
import {RootState} from '../../store';

// Define a type for the customer
interface Customer {
  id?: string;
  customerId?:string;
  name: string;
  mobile: string;
  email?: string;
  region?: string;
  floorNumber?: string;
  doorNumber?: string;
  houseType?: string;
  latitude?: string;
  longitude?: string;
  images: object[];
  customerProducts?: object[];
  // Add other properties as needed
}

interface CustomerApiState {
  data: Customer[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null|any;
  currentOperation: 'fetch' | 'add' | 'update' | 'delete' |'imgSave'| null;
  operationStatus: {
    fetch: 'idle' | 'loading' | 'succeeded' | 'failed';
    add: 'idle' | 'loading' | 'succeeded' | 'failed';
    update: 'idle' | 'loading' | 'succeeded' | 'failed';
    delete: 'idle' | 'loading' | 'succeeded' | 'failed';
    imagesave: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

const initialState: CustomerApiState = {
  data: [],
  status: 'idle',
  error: null,
  currentOperation: null,
  operationStatus: {
    fetch: 'idle',
    add: 'idle',
    update: 'idle',
    delete: 'idle',
    imagesave:'idle'
  },
};

const customerApiSlice = createSlice({
  name: 'customerApi',
  initialState,
  reducers: {
    resetCustomerApiStatus: state => {
      state.status = 'idle';
      state.error = null;
      state.currentOperation = null;
      state.operationStatus = {
        add: 'idle',
        fetch: 'idle',
        delete: 'idle',
        update: 'idle',
        imagesave:'idle'
      };
    },
    resetCustomerData:state=>{
      state.data=[]
    }
  },
  extraReducers: builder => {
    // Handle fetchCustomersApi
    builder
      .addCase(fetchCustomersApi.pending, state => {
        state.status = 'loading';
        state.operationStatus.fetch = 'loading';
        state.currentOperation = 'fetch';
      })
      .addCase(fetchCustomersApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.operationStatus.fetch = 'succeeded';
        state.data = action.payload;
        state.error = null;
        state.currentOperation = 'fetch';
      })
      .addCase(fetchCustomersApi.rejected, (state, action) => {
        state.status = 'failed';
        state.operationStatus.fetch = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'fetch';
      });

    // Handle addCustomerApi
    builder
      .addCase(addCustomerApi.pending, state => {
        state.status = 'loading';
        state.operationStatus.add = 'loading';
        state.currentOperation = 'add';
      })
      .addCase(addCustomerApi.fulfilled, (state, action) => {
        state.operationStatus.add = 'succeeded';
        state.status = 'succeeded';
        state.data.push(action.payload);
        state.error = null;
        state.currentOperation = 'add';
      })
      .addCase(addCustomerApi.rejected, (state, action) => {
        console.log(state,action, 'succesAfterFailur');
        state.status = 'failed';
        state.operationStatus.add = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'add';
      });

    // Handle updateCustomerApi
    builder
      .addCase(updateCustomerApi.pending, state => {
        state.status = 'loading';
        state.operationStatus.update = 'loading';
        state.currentOperation = 'update';
      })
      .addCase(updateCustomerApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.operationStatus.update = 'succeeded';
        const index = state.data.findIndex(
          customer => customer.id === action.payload.id,
        );
        if (index !== -1) {
          state.data[index] = action.payload;
        }
        state.error = null;
        state.currentOperation = 'update';
      })
      .addCase(updateCustomerApi.rejected, (state, action) => {
        state.status = 'failed';
        state.operationStatus.update = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'update';
      });

    // Handle deleteCustomerApi
    builder
      .addCase(deleteCustomerApi.pending, state => {
        state.status = 'loading';
        state.operationStatus.delete = 'loading';
        state.currentOperation = 'delete';
      })
      .addCase(deleteCustomerApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.operationStatus.delete = 'succeeded';
        state.data = state.data.filter(
          customer => customer.id !== action.payload,
        );
        state.error = null;
        state.currentOperation = 'delete';
      })
      .addCase(deleteCustomerApi.rejected, (state, action) => {
        state.status = 'failed';
        state.operationStatus.delete = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'delete';
      });
      builder
      .addCase(uploadCustomerImagesApi.pending, state => {
        state.status = 'loading';
        state.operationStatus.imagesave = 'loading';
        state.currentOperation = 'imgSave';
      })
      .addCase(uploadCustomerImagesApi.fulfilled, (state, action) => {
        console.log(action, "sdjkfdskjf")
        state.status = 'succeeded';
        state.operationStatus.imagesave = 'succeeded';
        state.error = null;
        state.currentOperation = 'imgSave';
      })
      .addCase(uploadCustomerImagesApi.rejected, (state, action) => {
        console.log(action, "sdjkfdskjf")
        state.status = 'failed';
        state.operationStatus.imagesave = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'imgSave';
      });
  },
});

export const {resetCustomerApiStatus, resetCustomerData} = customerApiSlice.actions;
export const customerSelector = (state: RootState) => state.customerApi;
export default customerApiSlice.reducer;
