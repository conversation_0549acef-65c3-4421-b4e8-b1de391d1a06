// import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
// import { createStackNavigator } from '@react-navigation/stack';
// import SplashScreen from '../screens/SplashScreen';
// import PhoneNumberScreen from '../screens/Auth/PhoneInputScreen';
// import AppNavigator from '../navigation/AppNavigator';

// type AuthStackParamList = {
//   Splash: undefined;
//   PhoneNumber: undefined;
//   Main: undefined;
// };

// interface AuthContextType {
//   isLoading: boolean;
//   setIsLoading: (value: boolean) => void;
//   isPhoneEntered: boolean;
//   setIsPhoneEntered: (value: boolean) => void;
// }

// interface AuthProviderProps {
//   children: ReactNode;
// }

// export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// const Stack = createStackNavigator<AuthStackParamList>();

// export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
//   const [isLoading, setIsLoading] = useState<boolean>(true);
//   const [isPhoneEntered, setIsPhoneEntered] = useState<boolean>(false);

//   useEffect(() => {
//     const timer = setTimeout(() => setIsLoading(false), 1000);
//     return () => clearTimeout(timer);
//   }, []);

//   return (
//     <AuthContext.Provider value={{ isLoading, setIsLoading, isPhoneEntered, setIsPhoneEntered }}>
//       <Stack.Navigator screenOptions={{ headerShown: false }}>
//         {isLoading ? (
//           <Stack.Screen name="Splash" component={SplashScreen} />
//         ) : !isPhoneEntered ? (
//           <Stack.Screen name="PhoneNumber" component={PhoneNumberScreen} />
//         ) : (
//           <Stack.Screen name="Main" component={AppNavigator} />
//         )}
//       </Stack.Navigator>
//       {children}
//     </AuthContext.Provider>
//   );
// };

// export const useAuth = (): AuthContextType => {
//   const context = useContext(AuthContext);
//   if (context === undefined) {
//     throw new Error('useAuth must be used within an AuthProvider');
//   }
//   return context;
// };