import {createSlice} from '@reduxjs/toolkit';
import {addCustomer} from '../thunks/customerThunks';

interface CustomersState {
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: CustomersState = {
  status: 'idle',
  error: null,
};

const customersSlice = createSlice({
  name: 'customers',
  initialState,
  reducers: {
    resetCustomerStatus: state => {
      state.status = 'idle';
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(addCustomer.pending, state => {
        state.status = 'loading';
      })
      .addCase(addCustomer.fulfilled, state => {
        state.status = 'succeeded';
        state.error = null;
      })
      .addCase(addCustomer.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      });
  },
});

export const {resetCustomerStatus} = customersSlice.actions;
export default customersSlice.reducer;
