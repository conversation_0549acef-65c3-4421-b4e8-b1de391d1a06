import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Image,
} from 'react-native';
import {Colors} from '../../../constants/colors';
import {imagePath} from '../../../constants/imagePath';
import {FontFamily} from '../../../constants/fontFamily';

interface DropdownProps {
  label: string;
  value: string;
  options: {label: string; value: string}[];
  onSelect: (value: string) => void;
  error?: string;
  touched?: boolean;
}

const Dropdown = ({
  label,
  value,
  options,
  onSelect,
  error,
  touched,
}: DropdownProps): React.JSX.Element => {
  const [modalVisible, setModalVisible] = useState(false);

  const selectedOption = options.find(option => option.value === value);

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          touched && error ? styles.dropdownError : null,
        ]}
        onPress={() => setModalVisible(true)}>
        <Text
          style={[
            styles.dropdownButtonText,
            !value ? styles.placeholderText : null,
          ]}>
          {selectedOption ? selectedOption.label : 'Select an option'}
        </Text>
        <Image source={imagePath.arrowDown_Icon} style={styles.dropdownIcon} />
      </TouchableOpacity>
      {touched && error ? <Text style={styles.errorText}>{error}</Text> : null}

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}>
          <TouchableOpacity activeOpacity={1} style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label}</Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={options}
              keyExtractor={item => item.value}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={() => {
                    onSelect(item.value);
                    setModalVisible(false);
                  }}>
                  <Text style={styles.optionText}>{item.label}</Text>
                  {item.value === value && (
                    <Image
                      source={imagePath.arrowDown_Icon}
                      style={styles.checkIcon}
                    />
                  )}
                </TouchableOpacity>
              )}
            />
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: FontFamily.REGULAR,
    color: Colors.dark,
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    height: 48,
  },
  dropdownError: {
    borderColor: Colors.danger,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: Colors.dark,
  },
  placeholderText: {
    color: Colors.listSeconary,
  },
  dropdownIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.listSeconary,
  },
  errorText: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: Colors.primary,
  },
  modalTitle: {
    fontSize: 18,
    // fontWeight: 'bold',
    color: Colors.white,
    // fontSize: 14,
    fontFamily: FontFamily.REGULAR,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.white,
    fontWeight: 'bold',
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.greyBackground,
  },
  optionText: {
    fontSize: 16,
    color: Colors.dark,
  },
  checkIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.primary,
  },
});

export default Dropdown;
