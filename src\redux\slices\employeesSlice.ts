// import { createSlice } from '@reduxjs/toolkit';
// import { fetchEmployees } from '../thunks/employeeThunks';

// // Define a type for the employee
// interface EmployeeType {
//   employeeTypeId: number;
//   employeeTypeName: string;
//   description: string;
// }

// interface Employee {
//   employeeId: number;
//   employeeName: string;
//   phoneNumber: string;
//   email: string;
//   image: string;
//   employeeType: EmployeeType;
// }

// interface EmployeesState {
//   data: Employee[];
//   status: 'idle' | 'loading' | 'succeeded' | 'failed';
//   error: string | null;
// }

// const initialState: EmployeesState = {
//   data: [],
//   status: 'idle',
//   error: null,
// };

// const employeesSlice = createSlice({
//   name: 'employees',
//   initialState,
//   reducers: {},
//   extraReducers: (builder) => {
//     builder
//       .addCase(fetchEmployees.pending, (state) => {
//         state.status = 'loading';
//       })
//       .addCase(fetchEmployees.fulfilled, (state, action) => {
//         state.status = 'succeeded';
//         state.data = action.payload;
//       })
//       .addCase(fetchEmployees.rejected, (state, action) => {
//         state.status = 'failed';
//         state.error = action.payload as string;
//       });
//   },
// });

// export default employeesSlice.reducer;
