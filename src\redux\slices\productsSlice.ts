import { createSlice } from '@reduxjs/toolkit';
import { fetchProducts } from '../thunks/productThunks';
import { RootState } from '../store';

// Define a type for the product
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  unitTypeId: string;
  category: {
    id: string;
    productId: string;
    name: string;
    description: string;
  };
  productImage: {
    id: string;
    productId: string;
    imageName: string;
    imageContent: string;
  };
}


interface ProductsState {
  productData: Product[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: ProductsState = {
  productData: [],
  status: 'idle',
  error: null,
};

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        console.log(state.status,action.payload, "callingLoading")
        state.status = 'succeeded';
        state.productData = action?.payload?.data;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      });
  },
});
export const productSelector = (state: RootState) => state.products;
export default productsSlice.reducer;