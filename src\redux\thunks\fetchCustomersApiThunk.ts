// import { createAsyncThunk } from '@reduxjs/toolkit';
// import axios from 'axios';
// import { BASE_URL } from '../../constants/colors';

// // Create the async thunk for fetching customers from API
// export const fetchCustomersApi = createAsyncThunk(
//   'customerApi/fetchCustomers',
//   async (_, { rejectWithValue }) => {
//     try {
//       const response = await axios.get(`http://172.21.1.248:8080/api/v1/Customer`);
//       return response.data;
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         return rejectWithValue(error.response?.data || error.message);
//       }
//       return rejectWithValue('An unknown error occurred');
//     }
//   }
// );