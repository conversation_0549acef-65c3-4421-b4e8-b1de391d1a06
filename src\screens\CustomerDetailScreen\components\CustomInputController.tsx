import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInputProps,
  Alert,
} from 'react-native';
import { Controller, Control, FieldError } from 'react-hook-form';
import { imagePath } from '../../../constants/imagePath';
import { Colors } from '../../../constants/colors';
interface CustomInputControllerProps extends TextInputProps {
  control: Control<any>;
  name: string;
  label: string;
  rules?: object;
  editable?: boolean;
  disabled?: boolean;
}

const CustomInputController: React.FC<CustomInputControllerProps> = ({
  control,
  name,
  label,
  rules = {},
  keyboardType = 'default',
  editable = true,
  disabled = false,
  secureTextEntry,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showError, setShowError] = useState(false);

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => {
        const hasError = !!error;

        // Custom onChange handler for phone number validation
        const handleTextChange = (text: string) => {
          if (name === 'phoneNumber') {
            // Only allow digits
            const digitsOnly = text.replace(/[^\d]/g, '');
            
            // Show error when trying to enter more than 10 digits
            if (digitsOnly.length > 10) {
              // Truncate to 10 digits and show alert
              Alert.alert('Invalid Input', 'Phone number must be 10 digits only');
              onChange(digitsOnly.substring(0, 10));
            } else {
              onChange(digitsOnly);
            }
          } else {
            onChange(text);
          }
        };

        return (
          <View style={styles.container}>
            <View
              style={[
                styles.inputContainer,
                hasError && styles.inputError,
                isFocused && styles.inputFocused,
                disabled && styles.inputDisabled,
              ]}
            >
              <Text
                style={[
                  styles.label,
                  (isFocused || value) ? styles.labelFloating : styles.labelPlaceholder,
                  disabled && styles.labelDisabled,
                ]}
              >
                {label}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  disabled && styles.inputTextDisabled,
                ]}
                value={value}
                onChangeText={handleTextChange}
                onBlur={() => {
                  onBlur();
                  setIsFocused(false);
                }}
                onFocus={() => setIsFocused(true)}
                editable={!disabled}
                keyboardType={keyboardType}
                secureTextEntry={secureTextEntry}
                maxLength={name === 'phoneNumber' ? 10 : undefined}
                {...rest}
              />
              {hasError && (
                <TouchableOpacity
                  onPress={() => setShowError(prev => !prev)}
                  style={styles.errorIconContainer}
                >
                  <Image source={imagePath.error_Icon} style={styles.errorIcon} />
                </TouchableOpacity>
              )}
            </View>
            {hasError && showError && (
              <Text style={styles.tooltip}>{error.message}</Text>
            )}
          </View>
        );
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    position: 'absolute',
    backgroundColor: Colors.white,
    paddingHorizontal: 4,
    fontSize: 10,
    left: 12,
    top: 14,
    color: Colors.listSeconary,
  },
  labelFloating: {
    top: -9,
    fontSize: 12,
    color: Colors.primary,
  },
  labelPlaceholder: {
    top: 14,
    fontSize: 16,
    color: Colors.listSeconary,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    paddingTop: 8,
  },
  inputFocused: {
    borderColor: Colors.primary,
  },
  inputError: {
    borderColor: Colors.danger,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.dark,
    paddingTop: 8,
  },
  inputDisabled: {
    backgroundColor: Colors.greyBackground,
  },
  inputTextDisabled: {
    color: 'grey',
  },
  labelDisabled: {
    color: 'grey',
  },
  errorIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.danger,
  },
  errorIconContainer: {
    padding: 8,
  },
  tooltip: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
});

export default CustomInputController;
