import {createAsyncThunk} from '@reduxjs/toolkit';
import axios from 'axios';
import {BASE_URL} from '../../../constants/colors';
import {Customer} from '../../../screens/CustomerListScreen';
import { ACTIVE_API_BASE_URL } from '../../../constants/api';

function sortCustomersByRouteSequence(customers: Customer[]): Customer[] {
  return [...customers].sort((a, b) => {
    if (!a.customerRouteDetail && !b.customerRouteDetail) {
      return 0;
    }
    if (!a.customerRouteDetail) {
      return 1;
    }
    if (!b.customerRouteDetail) {
      return -1;
    }
    return a.customerRouteDetail.sequence - b.customerRouteDetail.sequence;
  });
}

// Create the async thunk for fetching customers from API
export const fetchCustomersApi = createAsyncThunk(
  'customerApi/fetchCustomers',
  async (_, {rejectWithValue}) => {
    try {
      const response = await axios.get(
        `${ACTIVE_API_BASE_URL}/Customer`,
      );
      const customerData = sortCustomersByRouteSequence(response.data.data);
      console.log('customerData', customerData);
      return customerData;
      // return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);

// Create the async thunk for adding a customer via API
export const addCustomerApi = createAsyncThunk(
  'customerApi/addCustomer',
  async (customerData: any, {rejectWithValue}) => {
    try {
      console.log(customerData, 'customerDataComing');
      const response = await axios.post(
        `${ACTIVE_API_BASE_URL}/Customer`,
        customerData,
      );
      console.log(response?.data, 'responcecomesin');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);

// Create the async thunk for updating a customer
export const updateCustomerApi = createAsyncThunk(
  'customerApi/updateCustomer',
  async (
    {customerId, customerData1}: {customerId: string; customerData1: any},
    {rejectWithValue},
  ) => {
    try {
      console.log(customerId,customerData1, "payloadputDatas")
      const response = await axios.put(
        `${ACTIVE_API_BASE_URL}/Customer/${customerId}`,
        {customerId,...customerData1},
      );
      console.log(response,"errorsresdofj")
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);

// Create the async thunk for deleting a customer
export const deleteCustomerApi = createAsyncThunk(
  'customerApi/deleteCustomer',
  async (customerId: string, {rejectWithValue}) => {
    try {
      console.log('customerId', customerId);
      await axios.delete(
        `${ACTIVE_API_BASE_URL}/Customer/${customerId}`,
      );
      return customerId; // Return the ID for the reducer to remove from state
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);
export const uploadCustomerImagesApi = createAsyncThunk(
  'customer/uploadImages',
  async (
    {
      customerCode,
      formData,
    }: {
      customerCode: string;
      formData: FormData;
    },
    { rejectWithValue }
  ) => {
    try {
      const res = await axios.put(
        `${ACTIVE_API_BASE_URL}/Customer/${customerCode}/UploadImages`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      console.log(res, "resdjfkdskf")
      return res.data;
    } catch (err: any) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);
