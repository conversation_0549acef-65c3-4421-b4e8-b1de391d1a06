import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {Colors} from '../../constants/colors';
import {imagePath} from '../../constants/imagePath';
import {useNavigation} from '@react-navigation/native';

interface MapHeaderProps {
  title: string;
  onMenuPress: () => void;
  hasStarted: boolean;
  onStartPress: () => void;
}

const MapHeader = ({
  title,
  onMenuPress,
  hasStarted,
  onStartPress,
}: MapHeaderProps): React.JSX.Element => {
  const navigation = useNavigation();

  const handleDeliveryListPress = () => {
    navigation.navigate('CustomerDetail' as never);
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <TouchableOpacity onPress={onMenuPress} style={styles.iconButton}>
          <Image source={imagePath.menu_Icon} style={styles.icon} />
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>{title}</Text>

      <View style={styles.rightContainer}>
        {hasStarted ? (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            {/* <Text style={styles.dateText}>{new Date().toLocaleDateString()}</Text> */}
            <TouchableOpacity
              onPress={handleDeliveryListPress}
              style={{
                // width: 64,
                // height: 32,
                // borderRadius: 16,
                // backgroundColor: Colors.lightGreen,
                // justifyContent: 'center',
                // alignItems: 'center',
                // marginLeft: 8
                backgroundColor: '#E5FFFD', // Soft, light background
                paddingVertical: 6,
                paddingHorizontal: 14,
                borderRadius: 20,
              }}>
              <Text style={[styles.addButtonText, {color: Colors.greenColour}]}>
                Add
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity onPress={onStartPress} style={styles.startButton}>
            <Text style={styles.startButtonText}>Start</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  leftContainer: {
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightContainer: {
    width: 100,
    alignItems: 'flex-end',
  },
  title: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.white,
    // textAlign: 'center',
  },
  iconButton: {
    padding: 8,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: Colors.white,
  },
  addButtonText: {
    paddingHorizontal: 5,
    paddingVertical: 5,
    borderRadius: 5,
    color: '#0AA99C', 
    fontSize: 13,
    fontWeight: '600',
  },
  dateText: {
    fontSize: 14,
    color: Colors.white,
    fontWeight: '500',
  },
  startButton: {
    backgroundColor: '#FFBC00',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 5,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  startButtonText: {
    color: '#000000',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default MapHeader;
