// import React from 'react';
// import {SafeAreaView, StyleSheet} from 'react-native';
// import Main from './Main';
// import {Colors} from '../../constants/colors';

// export interface CustomerImage {
//   customerImageId: string;
//   customerId: string;
//   imageName: string;
//   imageType: string | null;
//   imageContent: string;
//   createdBy: string;
//   createdDate: string;
//   updatedBy: string | null;
//   updatedDate: string | null;
// }

// export interface CustomerProduct {
//   customerProductId: string;
//   customerId: string;
//   productId: string;
//   quantity: number;
//   startDate: string;
//   endDate: string;
//   createdBy: string;
//   createdDate: string;
//   updatedBy: string | null;
//   updatedDate: string | null;
// }

// export interface Customer {
//   customerId: string;
//   name: string;
//   mobile: string;
//   imageUrl?: string;
//   housetype: string;
//   region: string;
//   floorNumber: string;
//   cityId: string;
//   stateId: string;
//   countryId: string;
//   postalCodeId: string;
//   longitude: string;
//   lattitude: string;
//   landmark: string;
//   email: string;
//   doorNumber: string;
//   images: CustomerImage[];
//   customerProducts: CustomerProduct[];
//   createdBy: string;
//   createdDate: string;
//   updatedBy: string | null;
//   updatedDate: string | null;
// }

// const CustomerListScreen: React.FC = () => {
//   return (
//     <SafeAreaView style={styles.container}>
//       <Main />
//     </SafeAreaView>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: Colors.white,
//   },
// });

// export default CustomerListScreen;

// // import {useState} from 'react';
// // import {TouchableOpacity, Text} from 'react-native';
// // import DraggableFlatList, {
// //   RenderItemParams,
// //   DragEndParams,
// // } from 'react-native-draggable-flatlist';

// // interface ListItem {
// //   id: string;
// //   label: string;
// // }

// // function DraggableList1() {
// //   // const [data, setData] = useState<ListItem[]>([
// //   //   {id: '1', label: 'Item 1'},
// //   //   {id: '2', label: 'Item 2'},
// //   //   {id: '3', label: 'Item 3'},
// //   // ]);

// //   const [data, setData] = useState<ListItem[]>([
// //     {id: '1', label: 'Item 1'},
// //     {id: '2', label: 'Item 2'},
// //     {id: '3', label: 'Item 3'},
// //     {id: '4', label: 'Item 4'},
// //     {id: '5', label: 'Item 5'},
// //     {id: '6', label: 'Item 6'},
// //     {id: '7', label: 'Item 7'},
// //     {id: '8', label: 'Item 8'},
// //     {id: '9', label: 'Item 9'},
// //     {id: '10', label: 'Item 10'},
// //     {id: '11', label: 'Item 11'},
// //     {id: '12', label: 'Item 12'},
// //     {id: '13', label: 'Item 13'},
// //     {id: '14', label: 'Item 14'},
// //     {id: '15', label: 'Item 15'},
// //     {id: '16', label: 'Item 16'},
// //     {id: '17', label: 'Item 17'},
// //     {id: '18', label: 'Item 18'},
// //     {id: '19', label: 'Item 19'},
// //     {id: '20', label: 'Item 20'},
// //   ]);

// //   const renderItem = ({item, drag, isActive}: RenderItemParams<ListItem>) => {
// //     return (
// //       <TouchableOpacity
// //         style={{
// //           // backgroundColor: isActive ? 'lightblue' : 'white',
// //           padding: 20,
// //           borderBottomWidth: 1,
// //           borderBottomColor: '#ccc',
// //         }}
// //         onLongPress={drag}>
// //         <Text>{item.label}</Text>
// //       </TouchableOpacity>
// //     );
// //   };

// //   const handleDragEnd = ({data}: DragEndParams<ListItem>) => {
// //     setData(data);
// //   };

// //   return (
// //     <DraggableFlatList
// //       data={data}
// //       renderItem={renderItem}
// //       keyExtractor={item => item.id}
// //       onDragEnd={handleDragEnd}
// //     />
// //   );
// // }
