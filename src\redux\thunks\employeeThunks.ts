// import { createAsyncThunk } from '@reduxjs/toolkit';
// import axios from 'axios';
// import { BASE_URL } from '../../constants/colors';
// import { ACTIVE_API_BASE_URL } from '../../constants/api';
// const Url=`${ACTIVE_API_BASE_URL}/employees/type/1`

// export const fetchEmployees = createAsyncThunk(
//   'employees/fetchEmployees',
//   async (_, { rejectWithValue }) => {
//     try {
//       const response = await axios.get(Url);
//       console.log(response.data);
//       return response.data;
//     } catch (error: any) {
//     //   return rejectWithValue(error.message || 'Failed to fetch employees');
//     }
//   }
// );
