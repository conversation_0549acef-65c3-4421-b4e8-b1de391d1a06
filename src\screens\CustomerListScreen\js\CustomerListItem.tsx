// import React, {useState} from 'react';
// import {TouchableOpacity, Text, StyleSheet, View, Image} from 'react-native';
// import {Colors} from '../../constants/colors';
// import {imagePath} from '../../constants/imagePath';
// import ImageCarouselModal from '../../components/ImageCarouselModal';

// interface CustomerListItemProps {
//   item: any;
//   onPress: (id: string) => void;
//   onEdit?: (id: string) => void;
//   onDelete?: (id: string) => void;
//   onDrag?: () => void;
//   isDragging?: boolean;
// }

// const CustomerListItem = ({
//   item,
//   onPress,
//   onEdit,
//   onDelete,
//   onDrag,
//   isDragging,
// }: CustomerListItemProps): React.JSX.Element => {
//   const [carouselVisible, setCarouselVisible] = useState(false);

//   const showImageCarousel = () => {
//     if (item.pictures && item.pictures.length > 0) {
//       setCarouselVisible(true);
//     }
//   };

//   return (
//     <>
//       <TouchableOpacity
//         style={[styles.customerItem, isDragging && styles.dragging]}
//         onPress={() => onPress(item)}
//         onLongPress={onDrag}>
//         <View style={styles.leftContainer}>
//           {item.pictures && item.pictures.length > 0 ? (
//             <TouchableOpacity onPress={showImageCarousel}>
//               <Image
//                 source={{uri: item.pictures[0]}}
//                 style={styles.profileImage}
//               />
//             </TouchableOpacity>
//           ) : (
//             <View style={styles.profileImage} />
//           )}
//         </View>

//         <View style={styles.contentContainer}>
//           <Text style={styles.customerName}>{item.name}</Text>
//           <Text style={styles.customerPhone}>Phone: {item.phoneNumber}</Text>
//           <Text style={styles.customerDetails}>{item.comment}</Text>
//         </View>

//         <View style={styles.actionsContainer}>
//           {onEdit && (
//             <TouchableOpacity
//               style={styles.iconButton}
//               onPress={() => onEdit(item.id)}>
//               <Image source={imagePath.edit_Icon} style={styles.actionIcon} />
//             </TouchableOpacity>
//           )}
//         </View>
//       </TouchableOpacity>

//       {item.pictures && (
//         <ImageCarouselModal
//           visible={carouselVisible}
//           images={item.pictures}
//           onClose={() => setCarouselVisible(false)}
//         />
//       )}
//     </>
//   );
// };

// const styles = StyleSheet.create({
//   customerItem: {
//     backgroundColor: Colors.white,
//     padding: 12,
//     borderRadius: 8,
//     flexDirection: 'row',
//     elevation: 2,
//     shadowColor: '#000',
//     shadowOffset: {width: 0, height: 1},
//     shadowOpacity: 0.1,
//     shadowRadius: 1,
//   },
//   dragging: {
//     opacity: 0.9,
//     elevation: 5,
//     shadowOpacity: 0.2,
//   },
//   leftContainer: {
//     marginRight: 12,
//   },
//   profileImage: {
//     width: 50,
//     height: 50,
//     borderRadius: 25,
//     backgroundColor: '#E1E1E1',
//   },
//   contentContainer: {
//     flex: 1,
//     justifyContent: 'center',
//   },
//   customerName: {
//     fontSize: 16,
//     fontWeight: 'bold',
//     color: Colors.dark,
//     marginBottom: 2,
//   },
//   customerPhone: {
//     fontSize: 14,
//     color: Colors.listSeconary,
//     marginBottom: 2,
//   },
//   customerDetails: {
//     fontSize: 14,
//     color: Colors.listSeconary,
//   },
//   actionsContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   iconButton: {
//     padding: 8,
//   },
//   actionIcon: {
//     width: 20,
//     height: 20,
//     tintColor: Colors.listSeconary,
//   },
// });

// export default CustomerListItem;
