import {configureStore} from '@reduxjs/toolkit';
import regionsReducer from './slices/regionsSlice';
import customersReducer from './slices/customersSlice';
import productsReducer from './slices/productsSlice';
import deliveryApiReducer from './slices/deliveryApiSlice';
import customerApiReducer from './slices/Create/customerApiSlice';
import employeeApiReducer from './slices/employeeApiSlice';

export const store = configureStore({
  reducer: {
    regions: regionsReducer,
    employees: employeeApiReducer,
    customers: customersReducer,
    products: productsReducer,
    customerApi: customerApiReducer,
    deliveryApi: deliveryApiReducer,
    employeeApi: employeeApiReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
