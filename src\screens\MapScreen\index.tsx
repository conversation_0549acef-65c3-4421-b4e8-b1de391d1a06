
import React, {
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  ScrollView,
  Platform,
  Animated,
  PermissionsAndroid,
  RefreshControl,
} from 'react-native';
import MapView, {
  PROVIDER_GOOGLE,
  Marker,
  Polyline,
  Callout,
  Region,
  AnimatedRegion,
  MarkerAnimated,
} from 'react-native-maps';
import LottieView from 'lottie-react-native';
// import Geolocation from 'react-native-geolocation-service';

import axios from 'axios';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {Colors} from '../../constants/colors';
import MapHeader from './Header';
import Sidebar from './Sidebar';
import DeliveryBoyModal from './DeliveryBoyModal';
import RegionModal from './RegionModal';
import AllDeliveriesCompletedModal from './AllDeliveriesCompletedModal';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import {fetchRegions} from '../../redux/thunks/regionThunks';
import ApiContext from '../../context/ApiContext';
import {animationPath} from '../../constants/animationPath';
import {
  fetchDeliveriesApi,
  startDeliveryApi,
  updateDeliveryStatus,
} from '../../redux/thunks/deliveryApiThunks';
import Geolocation from '@react-native-community/geolocation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {resetDeliveryApiStatus} from '../../redux/slices/deliveryApiSlice';
import ImageCarouselModal from '../../components/ImageCarouselModal';
import { IMAGE_API_BASE_URL } from '../../constants/api';
// import SplashScreen from './PhontInput';

const {width, height} = Dimensions.get('window');

interface User {
  id: number;
  name: string;
  lat: number;
  lng: number;
  image: string;
  milkLitres: number;
}

const userData: User[] = [
  {
    id: 1,
    name: 'User A',
    lat: 10.690759458525603,
    lng: 77.0223777946036,
    image: 'https://via.placeholder.com/150',
    milkLitres: 2.5,
  },
  {
    id: 2,
    name: 'User B',
    lat: 10.690403149381341,
    lng: 77.02435774403715,
    image: 'https://via.placeholder.com/150',
    milkLitres: 1.0,
  },
  {
    id: 3,
    name: 'User C',
    lat: 10.6913128452911,
    lng: 77.021136758853,
    image: 'https://via.placeholder.com/150',
    milkLitres: 3.0,
  },
];

const getDistanceFromLatLonInKm = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 6371;
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Decode polyline points from Google Directions API
const decodePolyline = (
  encoded: string,
): {latitude: number; longitude: number}[] => {
  const points: {latitude: number; longitude: number}[] = [];
  let index = 0,
    lat = 0,
    lng = 0;

  while (index < encoded.length) {
    let shift = 0,
      result = 0;
    let byte: number;
    do {
      byte = encoded.charCodeAt(index++) - 63;
      result |= (byte & 0x1f) << shift;
      shift += 5;
    } while (byte >= 0x20);
    const dlat = result & 1 ? ~(result >> 1) : result >> 1;
    lat += dlat;

    shift = 0;
    result = 0;
    do {
      byte = encoded.charCodeAt(index++) - 63;
      result |= (byte & 0x1f) << shift;
      shift += 5;
    } while (byte >= 0x20);
    const dlng = result & 1 ? ~(result >> 1) : result >> 1;
    lng += dlng;

    points.push({latitude: lat / 1e5, longitude: lng / 1e5});
  }

  return points;
};

// Error Boundary Component
class ErrorBoundary extends React.Component<
  {children: React.ReactNode},
  {hasError: boolean}
> {
  state = {hasError: false};

  static getDerivedStateFromError() {
    return {hasError: true};
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Text>Something went wrong. Please try again.</Text>
        </View>
      );
    }
    return this.props.children;
  }
}

// Navigation Types
type RootStackParamList = {
  Location: undefined;
  CustomerList: {viewMode?: 'delivery'} | undefined;
};

const MapScreen = (): React.JSX.Element => {
  const navigation = useNavigation<any>();
  const dispatch = useAppDispatch();
  const {api} = useContext(ApiContext) || {};
  const {data: regions} = useAppSelector(state => state.regions);
  const {
    startDel,
    fetchLoad,
    Routedata,
    data: DelveriyData,
  } = useAppSelector(state => state.deliveryApi);
  console.log(startDel, DelveriyData, fetchLoad, Routedata, 'stasdjf');
  const mapRef = useRef<MapView>(null);
  const hasSetInitialRegion = useRef(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [deliveryBoyModalVisible, setDeliveryBoyModalVisible] = useState(false);
  const [regionModalVisible, setRegionModalVisible] = useState(false);
  const [headerTitle, setHeaderTitle] = useState('Delivery Map');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [deliveredUsers, setDeliveredUsers] = useState<number[]>([]);
  const [empUsId, setempUsId] = useState<string>();
  console.log(empUsId, 'skempsjdf');
  console.log(deliveredUsers, 'deliveredUser');
  // const [deliveryBoyLocation, setDeliveryBoyLocation] = useState<Region>({
  //   latitude: 10.69068,
  //   longitude:  77.02274,
  //   latitudeDelta: 0.01,
  //   longitudeDelta: 0.01,
  // });
  const [deliveryBoyLocation, setDeliveryBoyLocation] = useState<Region>({
    latitude: 11.016446644979654,
    longitude: 76.96129467022271,
    latitudeDelta: 0.0005,
    longitudeDelta: 0.00005,
  });
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  console.log(
    routeCoordinates,
    selectedUser,
    deliveryBoyLocation,
    'routescoordiante',
  );
  const [hasStarted, setHasStarted] = useState(false);
  const [displayText, setDisplayText] = useState('');
  const fullText = "Let's Go!";
  const [showStartMessage, setShowStartMessage] = useState(false);
  const [
    allDeliveriesCompletedModalVisible,
    setAllDeliveriesCompletedModalVisible,
  ] = useState(false);
  const [startAnimation, setStartAnimation] = useState(false);
  const [hasReachedDestination, setHasReachedDestination] =
    useState<boolean>(false);
  // Add state to store fetched deliveries
  const [fetchedDeliveries, setFetchedDeliveries] = useState<any>([]);
  console.log(
    deliveredUsers,
    fetchedDeliveries,
    startAnimation,
    'sjfsflkdjkldf',
  );
  const [heading, setHeading] = useState(0);
  const [prevLocation, setPrevLocation] = useState<any>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [expandedContainer, setExpandedContainer] = useState<number | null>(null);
  const [currentContainerIndex, setCurrentContainerIndex] = useState(0);

  console.log(heading, 'heaidn');
  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Location permission granted.');
          return true;
        } else {
          console.log('Location permission denied.');
          return false;
        }
      }
      return false;
    } catch (err) {
      console.error('Failed to request location permission:', err);
      return false;
    }
  };

  useEffect(() => {
    const checkPermission = async () => {
      const hasPermission = await requestLocationPermission();
      console.log('Has permission:', hasPermission);

      if (hasPermission) {
        // You can now get the location
        setHasLocationPermission(true);
        console.log('Permission granted, ready to fetch location');
      }
    };

    checkPermission();
  }, []);
  function calculateBearing(
    start: {latitude: any; longitude: any},
    end: {latitude: any; longitude: any},
  ) {
    const toRad = (deg: number) => (deg * Math.PI) / 180;
    const toDeg = (rad: number) => (rad * 180) / Math.PI;

    const lat1 = toRad(start.latitude);
    const lon1 = toRad(start.longitude);
    const lat2 = toRad(end.latitude);
    const lon2 = toRad(end.longitude);

    const dLon = lon2 - lon1;
    const y = Math.sin(dLon) * Math.cos(lat2);
    const x =
      Math.cos(lat1) * Math.sin(lat2) -
      Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon);
    const bearing = toDeg(Math.atan2(y, x));
    return (bearing + 360) % 360; // Normalize to 0-360°
  }

  const nearbyUsers =
    fetchedDeliveries?.length > 0
      ? fetchedDeliveries
          ?.map(delivery => ({
            id: delivery.deliveryId,
            name: delivery.customerDetail?.name || 'Unknown',
            lat: parseFloat(delivery.customerDetail?.lattitude || '0'),
            lng: parseFloat(delivery.customerDetail?.longitude || '0'),
            image: 'https://via.placeholder.com/150',
            milkLitres:
              delivery.products?.find(
                (p: {description: string}) => p.description === 'Milk',
              )?.quantity || 0,
          }))
          .filter(user => {
            const distance = getDistanceFromLatLonInKm(
              deliveryBoyLocation.latitude,
              deliveryBoyLocation.longitude,
              user.lat,
              user.lng,
            );
            return distance <= 10;
          })
      : userData.filter(user => {
          const distance = null;
          getDistanceFromLatLonInKm(
            deliveryBoyLocation.latitude,
            deliveryBoyLocation.longitude,
            user.lat,
            user.lng,
          );
          return distance;
        });

  useEffect(() => {
    console.log('nearbyUsers array:', JSON.stringify(nearbyUsers, null, 2));
  }, [nearbyUsers]);

  const fadeAnim = useRef(new Animated.Value(0)).current;

  // useEffect(() => {
  //   if (startAnimation) {
  //     fadeAnim.setValue(0); // Always reset before starting a new animation
  //     console.log('Start Animation Triggered');
  //     Animated.timing(fadeAnim, {
  //       toValue: 1,
  //       duration: 500,
  //       useNativeDriver: true,
  //     }).start();

  //     const timeout = setTimeout(() => {
  //       setStartAnimation(false);
  //     }, 3000);

  //     return () => clearTimeout(timeout);
  //   }
  // }, [startAnimation]);

  useEffect(() => {
    console.log('Checking location permission');
    if (!hasLocationPermission) return;

    console.log('Fetching initial location');
    Geolocation.getCurrentPosition(
      position => {
        const {latitude, longitude} = position.coords;
        console.log('Initial location:', {latitude, longitude});

        setDeliveryBoyLocation({
          latitude,
          longitude,
          latitudeDelta: 0.0005,
          longitudeDelta: 0.00005,
        });

        // hasSetInitialRegion.current = true;
        if (!hasSetInitialRegion.current) {
          mapRef.current?.animateToRegion(
            {
              latitude,
              longitude,
              latitudeDelta: 0.0005,
              longitudeDelta: 0.00005,
            },
            1000,
          );

          hasSetInitialRegion.current = true;
        }
      },
      err => {
        console.error('Error getting initial location:', err);
        setError('Failed to get initial location. Using default location.');
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );

    console.log('Setting up location watch');
    const watchId = Geolocation.watchPosition(
      position => {
        const {latitude, longitude} = position.coords;
        console.log('Location update:', {latitude, longitude});
        const newLocation = {latitude, longitude};

        if (prevLocation) {
          const newHeading = calculateBearing(prevLocation, newLocation);
          setHeading(newHeading);
        }

        setPrevLocation(newLocation);
        setDeliveryBoyLocation({
          ...newLocation,
          latitudeDelta: 0.000005,
          longitudeDelta: 0.0000005,
        });
      },
      err => {
        console.error('Error in location watch:', err);
        setError('Failed to watch location');
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 1,
        useSignificantChanges: false,
        //  interval: 1000,         // Optional (Android): request update every 1s
        // fastestInterval: 1
        // Remove `interval` for real-time (event-based) updates
      },
    );

    return () => {
      console.log('Clearing location watch');
      Geolocation.clearWatch(watchId);
    };
  }, [hasLocationPermission]);
  // Fetch regions and handle region selection
  useEffect(() => {
    console.log('Fetching regions');
    const fetchRegionsAsync = async () => {
      try {
        await dispatch(fetchRegions()).unwrap();
        if (api) {
          const region = await api.getSelectedRegion();
          if (region) {
            setHeaderTitle(`${region.name}`);
          } else {
            setRegionModalVisible(true);
          }
        }
      } catch (err) {
        console.error('Error fetching regions:', err);
        setError('Failed to fetch regions');
      }
    };

    fetchRegionsAsync();
  }, [dispatch, api]);

  // Fetch route when a user is selected and delivery has started
  useEffect(() => {
    if (!selectedUser || !hasStarted) {
      setRouteCoordinates([]);
      return;
    }

    const fetchRoute = async () => {
      try {
        console.log('Fetching route for user:', selectedUser.name);
        console.log(
          'Origin:',
          deliveryBoyLocation.latitude,
          deliveryBoyLocation.longitude,
        );
        console.log('Destination:', selectedUser.lat, selectedUser.lng);
        const origin = `${deliveryBoyLocation.latitude},${deliveryBoyLocation.longitude}`;
        const destination = `${selectedUser.lat},${selectedUser.lng}`;
        const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=AIzaSyBz-AO7iQqzwZPvFXHNJHASj4k7DN4R5hs`;
        console.log(
          'Directions API URL:',
          url.replace(/key=[^&]+/, 'key=REDACTED'),
          url,
        );
        const response = await axios.get(url);
        console.log('Directions API response status:', response.data.status);

        if (response.data.status !== 'OK') {
          const errorMessage = response.data.error_message || 'Unknown error';
          throw new Error(
            `Directions API error: ${response.data.status} - ${errorMessage}`,
          );
        }

        const points = response.data.routes[0].overview_polyline.points;
        const decodedPoints = decodePolyline(points);
        setRouteCoordinates(decodedPoints);

        console.log('Route fetched:', {
          distance: response.data.routes[0].legs[0].distance.text,
          duration: response.data.routes[0].legs[0].duration.text,
        });

        if (mapRef.current) {
          mapRef.current.fitToCoordinates(
            [
              {
                latitude: deliveryBoyLocation.latitude,
                longitude: deliveryBoyLocation.longitude,
              },
              {latitude: selectedUser.lat, longitude: selectedUser.lng},
            ],
            {
              edgePadding: {top: 100, right: 100, bottom: 100, left: 100},
              animated: true,
            },
          );
        }
      } catch (err: any) {
        console.error('Error fetching route:', err.message);
        setError(`Failed to fetch route: ${err.message}`);
      }
    };

    fetchRoute();
  }, [selectedUser, deliveryBoyLocation, hasStarted]);

  // Handle letter-by-letter animation
  useEffect(() => {
    if (showStartMessage && displayText.length < fullText.length) {
      const timer = setTimeout(() => {
        setDisplayText(fullText.substring(0, displayText.length + 1));
      }, 150); // Speed of letter appearance
      return () => clearTimeout(timer);
    } else if (showStartMessage && displayText.length === fullText.length) {
      // Hide message after text is fully displayed and a short delay
      setTimeout(() => {
        setShowStartMessage(false);

        // Select the first customer if none is selected
        if (!selectedUser && nearbyUsers.length > 0) {
          setSelectedUser(nearbyUsers[0]);
        }
      }, 1500);
    }
  }, [showStartMessage, displayText, nearbyUsers, selectedUser]);

  useEffect(() => {
    // Check if tracking is already active when component mounts
    const checkTrackingStatus = async () => {
      try {
        const trackingActive = await AsyncStorage.getItem('tracking_active');
        if (trackingActive === 'true') {
          console.log('Tracking was already active, restoring state');
          setHasStarted(true);
          setDeliveredUsers([]);
          setSelectedUser(null);
          setFetchedDeliveries([]);
          const now = new Date();
          const isoNow = now.toISOString();
          console.log(isoNow);

          console.log('Formatted Today:', isoNow);
          dispatch(fetchDeliveriesApi({userId: empUsId, deliveryDate: isoNow}))
            .unwrap()
            .then(deliveries => {
              console.log('Restored deliveries:', deliveries);
              setFetchedDeliveries(deliveries.data);

              // Try to restore delivered users from AsyncStorage
              AsyncStorage.getItem('delivered_users')
                .then(deliveredUsersJson => {
                  if (deliveredUsersJson) {
                    const parsedDeliveredUsers = JSON.parse(deliveredUsersJson);
                    setDeliveredUsers(parsedDeliveredUsers);

                    // Find the next undelivered user to select
                    if (deliveries.data && deliveries.data.length > 0) {
                      const nextUndeliveredUser = deliveries.data.find(
                        (delivery: any) =>
                          !parsedDeliveredUsers.includes(delivery.deliveryId),
                      );

                      if (nextUndeliveredUser) {
                        setSelectedUser({
                          id: nextUndeliveredUser.deliveryId,
                          name:
                            nextUndeliveredUser.customerDetail?.name ||
                            'Unknown',
                          lat: parseFloat(
                            nextUndeliveredUser.customerDetail?.lattitude ||
                              '0',
                          ),
                          lng: parseFloat(
                            nextUndeliveredUser.customerDetail?.longitude ||
                              '0',
                          ),
                          image: 'https://via.placeholder.com/150',
                          milkLitres:
                            nextUndeliveredUser.products?.find(
                              (p: {description: string}) =>
                                p.description === 'Milk',
                            )?.quantity || 0,
                        });
                      }
                    }
                  }
                })
                .catch(error => {
                  console.error('Error restoring delivered users:', error);
                });
            })
            .catch(error => {
              console.error('Error fetching deliveries:', error);
            });
        }
      } catch (error) {
        console.error('Error checking tracking status:', error);
      }
    };

    if (empUsId) checkTrackingStatus();
  }, [dispatch, empUsId]);

  const handleStart = async () => {
    console.log('iamclasinagainf');
    if (!startAnimation) {
      setStartAnimation(true);
    }

    // Store tracking state in AsyncStorage
    try {
      await AsyncStorage.setItem('tracking_active', 'true');
      console.log('Tracking state stored in AsyncStorage');
    } catch (error) {
      console.error('Error storing tracking state:', error);
    }
    if (empUsId) {
      dispatch(startDeliveryApi({empId: empUsId}));
    }
    setHasStarted(true);
    setShowStartMessage(true);
    setDisplayText('');

    // Start fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const handleDelivered = (userId: number) => {
    console.log('🟡 handleDelivered called for userId:', userId);

    const delivery = fetchedDeliveries?.find(d => d.deliveryId === userId);
    console.log('🔍 Matching delivery found:', delivery);

    if (delivery) {
      console.log(
        '📤 Dispatching updateDeliveryStatus for deliveryId:',
        delivery.deliveryId,
      );
      console.log('📤 Dispatching updateDeliveryStatus ::', delivery);

      dispatch(
        updateDeliveryStatus({
          deliveryId: delivery.deliveryId.toString(),
          customerId:
            delivery.customerDetail?.customerId || delivery.customerId,
        }),
      )
        .unwrap()
        .then(() => {
          console.log('✅ Delivery status updated successfully');

          const updatedDeliveredUsers = [...deliveredUsers, userId];
          console.log(
            '📦 Updated delivered users list:',
            updatedDeliveredUsers,
          );

          setDeliveredUsers(updatedDeliveredUsers);

          AsyncStorage.setItem(
            'delivered_users',
            JSON.stringify(updatedDeliveredUsers),
          )
            .then(() => {
              console.log('💾 delivered_users saved to AsyncStorage');
            })
            .catch(error =>
              console.error('❌ Error storing delivered users:', error),
            );

          if (selectedUser?.id === userId) {
            console.log('🔄 Clearing selected user');
            setSelectedUser(null);
          }

          const nextUndeliveredUser = nearbyUsers.find(
            (user, index) =>
              !updatedDeliveredUsers.includes(user.id) &&
              ![userId].includes(user.id) &&
              index > nearbyUsers.findIndex(u => u.id === userId),
          );

          if (nextUndeliveredUser) {
            console.log(
              '➡️ Next undelivered user selected:',
              nextUndeliveredUser,
            );
            setSelectedUser(nextUndeliveredUser);
            // Note: Removed scroll logic since we now show single container
          } else {
            console.log('🚫 No next undelivered user found');
          }
        })
        .catch(error => {
          console.error('❌ Failed to update delivery status:', error);
          setError('Failed to mark delivery as completed');
        });
    } else {
      console.warn('⚠️ Delivery not found for userId:', userId);

      // const updatedDeliveredUsers = [...deliveredUsers, userId];
const updatedDeliveredUsers = [...new Set([...deliveredUsers, userId])];
      setDeliveredUsers(updatedDeliveredUsers);

      if (selectedUser?.id === userId) {
        console.log('🔄 Clearing selected user (fallback)');
        setSelectedUser(null);
      }

      const nextUndeliveredUser = nearbyUsers.find(
        (user, index) =>
          !deliveredUsers.includes(user.id) &&
          ![userId].includes(user.id) &&
          index > nearbyUsers.findIndex(u => u.id === userId),
      );

      if (nextUndeliveredUser) {
        console.log(
          '➡️ (Fallback) Next undelivered user selected:',
          nextUndeliveredUser,
        );
        setSelectedUser(nextUndeliveredUser);
      } else {
        console.log('🚫 (Fallback) No next undelivered user found');
      }
    }
  };
const sortedDeliveries = useMemo(() => {
  return [...fetchedDeliveries].sort((a, b) => {
    const aDelivered = deliveredUsers.includes(a.deliveryId);
    const bDelivered = deliveredUsers.includes(b.deliveryId);
    return aDelivered === bDelivered ? 0 : aDelivered ? 1 : -1;
  });
}, [fetchedDeliveries, deliveredUsers]);

  const handleMenuPress = () => {
    console.log('Menu pressed');
    setSidebarVisible(true);
  };

  const handleCloseSidebar = () => {
    console.log('Closing sidebar');
    setSidebarVisible(false);
  };

  const navigateToCustomerList = () => {
    console.log('Navigating to CustomerList');
    try {
      navigation.navigate('CustomerList');
      setSidebarVisible(false);
    } catch (err) {
      console.error('Navigation error:', err);
      setError('Failed to navigate to CustomerList');
    }
  };

  const navigateToDeliveryList = () => {
    // Fetch delivery list when navigating to DeliveryList
    const now = new Date();
    const isoNow = now.toISOString();
    console.log(isoNow);

    console.log('Formatted Today:', isoNow);
    dispatch(fetchDeliveriesApi({userId: empUsId, deliveryDate: isoNow}))
      .unwrap()
      .then(deliveries => {
        console.log('Fetched deliveries:', deliveries);
        // Navigate to DeliveryList screen and pass the fetched deliveries
        if (
          !deliveries.success ||
          !deliveries.data ||
          deliveries.data.length === 0
        ) {
          api?.showToast('No deliveries found for today');
        }

        navigation.navigate('DeliveryList', {
          deliveries: deliveries.data || [],
        } as never);
      })

      .catch(error => {
        console.error('Error fetching deliveries:', error);
        api?.showToast('Failed to fetch deliveries');
        navigation.navigate('DeliveryList', {
          deliveries: [],
        } as never);
      });
    dispatch(resetDeliveryApiStatus());

    setSidebarVisible(false);
  };

  const navigateToChooseRegion = () => {
    console.log('Navigating to ChooseRegion');
    setRegionModalVisible(true);
    setSidebarVisible(false);
  };

  const navigateToPhoneInputScreen = () => {
    navigation.navigate('PhoneNumber');
    setSidebarVisible(false);
  };

  const handleSelectDeliveryBoy = (employeeId: number) => {
    console.log('Selected delivery boy ID:', employeeId);
    setDeliveryBoyModalVisible(false);
  };

  const handleSelectRegion = (regionId: string) => {
    console.log('Selecting region:', regionId);
    // return
    try {
      const selectedRegion = regions?.find(
        (region: {storeId: string; name: string}) =>
          region.storeId === regionId,
      );
      if (selectedRegion) {
        api?.storeSelectedRegion(regionId, selectedRegion.name);
        setHeaderTitle(`${selectedRegion.name}`);
      }
      setRegionModalVisible(false);
    } catch (err) {
      console.error('Error selecting region:', err);
      setError('Failed to select region');
    }
  };

  const isNextCustomerToDeliver = (userId: number) => {
    if (deliveredUsers.length === 0) {
      // If no deliveries yet, only the first customer is enabled
      return userId === nearbyUsers[0]?.id;
    } else {
      // Find the index of the last delivered user
      const lastDeliveredIndex = nearbyUsers.findIndex(
        user => user.id === deliveredUsers[deliveredUsers.length - 1],
      );

      // The next user is the one after the last delivered
      const nextUserIndex = lastDeliveredIndex + 1;

      // If we have a next user, return true if this is that user
      if (nextUserIndex < nearbyUsers.length) {
        return userId === nearbyUsers[nextUserIndex].id;
      }

      return false;
    }
  };

  useEffect(() => {
    console.log(
      nearbyUsers.length > 0 &&
        deliveredUsers.length === nearbyUsers.length &&
        hasStarted &&
        !allDeliveriesCompletedModalVisible,
      'doifdsijfk',
    );
    if (
      nearbyUsers.length > 0 &&
      deliveredUsers.length === nearbyUsers.length &&
      hasStarted &&
      !allDeliveriesCompletedModalVisible
    ) {
      setAllDeliveriesCompletedModalVisible(true);
      // setFetchedDeliveries([]);
    }
  }, [
    deliveredUsers,
    nearbyUsers,
    hasStarted,
    allDeliveriesCompletedModalVisible,
  ]);

  const handleReturnToMap = async () => {
    setAllDeliveriesCompletedModalVisible(false);
    setHasStarted(false);
    // Clear tracking state in AsyncStorage
    try {
      await AsyncStorage.removeItem('tracking_active');
      await AsyncStorage.removeItem('delivered_users');
      console.log('Tracking state cleared from AsyncStorage');
    } catch (error) {
      console.error('Error clearing tracking state:', error);
    }
    AsyncStorage.removeItem('tracking_active');
    AsyncStorage.removeItem('delivered_users');
    // Reset delivery state
    setHasStarted(false);
    // setDeliveredUsers([]);
  };

  // Add this function to check distance between two coordinates
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ) => {
    const R = 6371e3; // Earth radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // in meters
  };

  // Add this to the useEffect that watches location updates
  useEffect(() => {
    if (selectedUser && hasStarted) {
      // Check if delivery boy is within 50 meters of the destination
      const distance = calculateDistance(
        deliveryBoyLocation.latitude,
        deliveryBoyLocation.longitude,
        selectedUser.lat,
        selectedUser.lng,
      );

      // If within 50 meters, consider as reached destination
      setHasReachedDestination(distance <= 10000);
    } else {
      setHasReachedDestination(false);
    }
  }, [deliveryBoyLocation, selectedUser, hasStarted]);

  // if (error) {
  //   return (
  //     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
  //       <Text style={{ color: 'red', textAlign: 'center', marginBottom: 20 }}>
  //         Error: {error}
  //       </Text>
  //       <Text style={{ textAlign: 'center', marginBottom: 20 }}>
  //         If this is a Directions API error, please check your API key and ensure the Directions API is enabled in the Google Cloud Console.
  //       </Text>
  //       <TouchableOpacity
  //         style={styles.errorButton}
  //         onPress={() => setError(null)}
  //       >
  //         <Text style={styles.errorButtonText}>Retry</Text>
  //       </TouchableOpacity>
  //     </View>
  //   );
  // }

  console.log('Rendering MapScreen');
  const getStoredEmployeeData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('employeeData');
      if (storedData) {
        const data = JSON.parse(storedData);
        console.log(data, 'Parsed employee data');
        setempUsId(data?.employeeId);
        return data;
      } else {
        console.log('No data found');
      }
    } catch (error) {
      console.error('Error reading employee data:', error);
    }
  };
  useEffect(() => {
    getStoredEmployeeData();
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      if (startDel === 'succeeded') {
        try {
          const now = new Date().toISOString();
          const res = await dispatch(
            fetchDeliveriesApi({userId: empUsId, deliveryDate: now}),
          ).unwrap();
          setFetchedDeliveries(res?.data || []);
        } catch (error) {
          setFetchedDeliveries([]);
        } finally {
          dispatch(resetDeliveryApiStatus());
        }
      }
    };

    fetchData();
  }, [startDel]);

  useEffect(() => {
    if (fetchLoad === 'succeeded') {
      setFetchedDeliveries(DelveriyData?.data || []);
      dispatch(resetDeliveryApiStatus());
    } else if (fetchLoad === 'failed') {
      setFetchedDeliveries([]);
      dispatch(resetDeliveryApiStatus());
    }
  }, [fetchLoad]);

  useFocusEffect(
    useCallback(() => {
      const fetchDeliveries = async () => {
        try {
          const now = new Date();
          const isoNow = now.toISOString();
          console.log('Fetching deliveries at:', isoNow);

          const response = await dispatch(
            fetchDeliveriesApi({userId: empUsId, deliveryDate: isoNow}),
          );
          console.log(response?.payload?.errors[0], 'responcedjf');
          if (
            response?.payload?.errors[0] ===
            'No deliveries found for the employee'
          ) {
            setHasStarted(false);
          }
          console.log('Saved delivered users to AsyncStorage');
        } catch (error) {
          console.error('Error fetching deliveries:', error);
        }
      };

      if (empUsId) {
        fetchDeliveries();
      }
    }, [empUsId, dispatch]),
  );

  useEffect(() => {
    if (fetchedDeliveries?.length) {
      const deliveredIdsFromApi = fetchedDeliveries
        .filter((d: {isDelivered: any}) => d.isDelivered)
        .map((d: {deliveryId: any}) => d.deliveryId);

      if (deliveredIdsFromApi.length === fetchedDeliveries.length) {
        setDeliveredUsers([]);
        setHasStarted(false);
        setShowStartMessage(false);
        setDisplayText('');

        AsyncStorage.removeItem('tracking_active');
        AsyncStorage.removeItem('delivered_users');

        console.log('All deliveries were delivered. Resetting state.');
      } else {
        setDeliveredUsers(deliveredIdsFromApi);
        setHasStarted(true);
        setShowStartMessage(true);
        setDisplayText('');

        AsyncStorage.setItem('tracking_active', 'true');
        AsyncStorage.setItem(
          'delivered_users',
          JSON.stringify(deliveredIdsFromApi),
        );
      }
    }
  }, [fetchedDeliveries]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // await checkAndStartRoute();
    } catch (error) {
      console.error('Error refreshing:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleImagePress = (delivery: any) => {
    console.log("handleImagePress ::",  delivery)
    // Check if delivery has images
    const images = [];

    // Add profile image as fallback
    images.push('https://via.placeholder.com/150');

    // If customer has images, use those
    if (delivery?.customerImages && Array.isArray(delivery.customerImages)) {
      const customerImages = delivery.customerImages
        .filter((img: any) => img?.imageName)
        .map((img: any) => `${IMAGE_API_BASE_URL}/${delivery.customerDetail.customerId}/${img.imageName}`);

        console.log("customerImages ::", customerImages)
      if (customerImages.length > 0) {
        images.length = 0; // Clear the fallback
        images.push(...customerImages);
      }
    }

    setSelectedImages(images);
    setImageModalVisible(true);
  };

  const handleSkip = (userId: number) => {
    console.log('Skipping delivery for userId:', userId);
    // Find next undelivered user
    const currentIndex = fetchedDeliveries?.findIndex(d => d.deliveryId === userId) || 0;
    const nextIndex = (currentIndex + 1) % (fetchedDeliveries?.length || 1);
    setCurrentContainerIndex(nextIndex);

    // If there's a next delivery, select it
    if (fetchedDeliveries && fetchedDeliveries[nextIndex]) {
      const nextDelivery = fetchedDeliveries[nextIndex];
      const nextUser = {
        id: nextDelivery.deliveryId,
        name: nextDelivery.customerDetail?.name || 'Unknown',
        lat: parseFloat(nextDelivery.customerDetail?.lattitude || '0'),
        lng: parseFloat(nextDelivery.customerDetail?.longitude || '0'),
        image: 'https://via.placeholder.com/150',
        milkLitres: nextDelivery.products?.find(
          (p: {description: string; quantity: number}) => p.description === 'Milk'
        )?.quantity || 0,
      };
      setSelectedUser(nextUser);
    }

    // Close expanded view
    setExpandedContainer(null);
  };

  const handleContainerPress = (userId: number) => {
    if (expandedContainer === userId) {
      setExpandedContainer(null);
    } else {
      setExpandedContainer(userId);
    }
  };

  return (
    <ErrorBoundary>
      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{flexGrow: 1}}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View style={styles.container}>
          <MapHeader
            title={headerTitle}
            onMenuPress={handleMenuPress}
            hasStarted={hasStarted}
            onStartPress={handleStart}
          />

          <MapView
            ref={mapRef}
            provider={PROVIDER_GOOGLE}
            style={[styles.map, {height: height - 56}]}
            initialRegion={deliveryBoyLocation}>
            <Marker
              // coordinate={{
              //   latitude: deliveryBoyLocation.latitude,
              //   longitude: deliveryBoyLocation.longitude,
              // }}
              rotation={heading}
              flat
              coordinate={deliveryBoyLocation}
              anchor={{x: 0.5, y: 1}}
              title="Delivery Boy">
              <Image
                source={require('../../assets/images/motorcycle_icon.png')}
                style={{width: 40, height: 40}}
                resizeMode="cover"
              />
            </Marker>

            {sortedDeliveries?.map(user => (
              <Marker
                key={user.deliveryId}
                coordinate={{
                  latitude: parseFloat(user.customerDetail.lattitude),
                  longitude: parseFloat(user.customerDetail.longitude),
                }}
                anchor={{x: 0.5, y: 1}}
                title={user.name}>
                <Image
                  source={
                    deliveredUsers.includes(user.deliveryId)
                      ? require('../../assets/images/delivered.png')
                      : require('../../assets/google_map_pin_icon.png')
                  }
                  style={{width: 40, height: 40}}
                  resizeMode="contain"
                />
                <Callout>
                  <View style={styles.marker}>
                    <Image
                      source={{uri: user.image}}
                      style={styles.markerImage}
                    />
                    <Text style={styles.markerText}>{user.name}</Text>
                  </View>
                </Callout>
              </Marker>
            ))}

            {routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor="#28c76f"
                strokeWidth={6}
                lineCap="round"
                lineJoin="round"
                geodesic
              />
            )}
          </MapView>

          {/* Current Location Button */}
          <TouchableOpacity
            style={styles.currentLocationButton}
            onPress={() => {
              if (mapRef.current && deliveryBoyLocation) {
                mapRef.current.animateToRegion(
                  {
                    latitude: deliveryBoyLocation.latitude,
                    longitude: deliveryBoyLocation.longitude,
                    latitudeDelta: 0.0005,
                    longitudeDelta: 0.00005,
                  },
                  1000,
                );
              }
            }}>
            <Image
              source={require('../../assets/images/current_location.png')}
              style={styles.currentLocationIcon}
              resizeMode="contain"
            />
          </TouchableOpacity>

          {nearbyUsers.length > 0 &&
            deliveredUsers.length === nearbyUsers.length && (
              <View style={styles.allDeliveredContainer}>
                <Text style={styles.allDeliveredText}>
                  All products delivered
                </Text>
              </View>
            )}

          {/* Single Container for Current Delivery */}
          {(() => {
            // Find the current delivery to show
            const currentDelivery = fetchedDeliveries?.find(delivery => {
              const user = nearbyUsers?.find(
                user => user.name === delivery.customerDetail?.name,
              ) || {
                id: delivery.deliveryId,
                name: delivery.customerDetail?.name || 'Unknown',
                // ... user properties
              };
              
              // Show selected user or next customer to deliver
              return selectedUser?.id === user.id || 
                     (!selectedUser && isNextCustomerToDeliver(user.id));
            });

            if (!currentDelivery) return null;

            return (
              <View style={styles.singleContainer}>
                <TouchableOpacity style={[styles.singleItem, /* conditional styles */]}>
                  {/* Single delivery container content */}
                </TouchableOpacity>
              </View>
            );
          })()}

          {/* Horizontal Scroll Container - One at a time */}
          <View style={styles.carouselContainer}>
            <ScrollView
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              style={{width: '100%', height: '100%'}}
              contentContainerStyle={{alignItems: 'center'}}>
              {fetchedDeliveries?.map((delivery, index) => {
                const user = nearbyUsers?.find(
                  user => user.name === delivery.customerDetail?.name,
                ) || {
                  id: delivery.deliveryId,
                  name: delivery.customerDetail?.name || 'Unknown',
                  lat: parseFloat(delivery.customerDetail?.longitude || '0'),
                  lng: parseFloat(delivery.customerDetail?.lattitude || '0'),
                  milkLitres:
                    delivery.products?.find(
                      (p: {description: string; quantity: number}) =>
                        p.description === 'Milk',
                    )?.quantity || 0,
                };

                const isExpanded = expandedContainer === user.id;

                const formatQuantity = (
                  quantity: number,
                  unitType: string,
                ): string => {
                  if (
                    unitType.toLowerCase() === 'litre' ||
                    unitType.toLowerCase() === 'liter'
                  ) {
                    const liters = Math.floor(quantity);
                    const milliliters = Math.round((quantity - liters) * 1000);
                    const parts = [];

                    if (liters > 0)
                      parts.push(`${liters} litre${liters > 1 ? 's' : ''}`);
                    if (milliliters > 0) parts.push(`${milliliters} ml`);

                    return parts.join(' and ');
                  }

                  // Fallback for other unit types
                  return `${quantity} ${unitType}`;
                };

                return (
                  <TouchableOpacity
                    key={user.id}
                    style={[
                      styles.horizontalCarouselItem,
                      isExpanded && styles.expandedCarouselItem,
                      deliveredUsers.includes(user.id) &&
                        styles.deliveredCarouselItem,
                      selectedUser?.id === user.id &&
                        styles.selectedCarouselItem,
                      !deliveredUsers.includes(user.id) &&
                        selectedUser?.id !== user.id &&
                        !isNextCustomerToDeliver(user.id) &&
                        styles.upcomingCarouselItem,
                      !hasStarted && styles.carouselItemBeforeStart,
                    ]}
                    onPress={() => handleContainerPress(user.id)}
                    activeOpacity={0.8}>

                    {!isExpanded ? (
                      // Collapsed View: Image + Name + Buttons
                      <View style={styles.collapsedContainer}>
                        <TouchableOpacity onPress={() => handleImagePress(delivery)}>
                          <Image
                            source={{ uri: delivery.customerImages?.[0]?.imageName || 'https://via.placeholder.com/150' }}
                            style={styles.collapsedImage}
                            resizeMode="cover"
                          />
                        </TouchableOpacity>

                        <Text style={styles.collapsedName}>{user.name}</Text>

                        <View style={styles.buttonRow}>
                          {hasStarted && (
                            <TouchableOpacity
                              style={[
                                styles.deliverButton,
                                deliveredUsers.includes(user.id)
                                  ? styles.completedButton
                                  : (!isNextCustomerToDeliver(user.id) ||
                                      !hasReachedDestination) &&
                                    styles.deliveredButtonDisabled,
                              ]}
                              onPress={(e) => {
                                e.stopPropagation();
                                handleDelivered(user.id);
                              }}
                              disabled={
                                deliveredUsers.includes(user.id) ||
                                !isNextCustomerToDeliver(user.id) ||
                                !hasReachedDestination
                              }>
                              <Text style={styles.deliverButtonText}>
                                {deliveredUsers.includes(user.id)
                                  ? 'Completed'
                                  : hasReachedDestination &&
                                    isNextCustomerToDeliver(user.id)
                                  ? 'Deliver'
                                  : 'Reach'}
                              </Text>
                            </TouchableOpacity>
                          )}

                          <TouchableOpacity
                            style={styles.skipButton}
                            onPress={(e) => {
                              e.stopPropagation();
                              handleSkip(user.id);
                            }}>
                            <Text style={styles.skipButtonText}>Skip</Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    ) : (
                      // Expanded View: Top Half Image + Bottom Half Details
                      <View style={styles.expandedContainer}>
                        <View style={styles.expandedImageContainer}>
                          <TouchableOpacity onPress={() => handleImagePress(delivery)}>
                            <Image
                              source={{ uri: delivery.customerImages?.[0]?.imageName || 'https://via.placeholder.com/150' }}
                              style={styles.expandedImage}
                              resizeMode="cover"
                            />
                          </TouchableOpacity>
                        </View>
                        <View style={styles.expandedDetailsContainer}>
                          <Text style={styles.expandedName}>{user.name}</Text>

                          {delivery?.products?.length > 0 && (
                            <View style={styles.productInfo}>
                              {delivery.products.map(
                                (
                                  product: {
                                    description: string;
                                    quantity: number;
                                    unitType: string;
                                  },
                                  productIndex: number,
                                ) => (
                                  <Text key={productIndex} style={styles.productText}>
                                    {product.description}:{' '}
                                    {formatQuantity(
                                      product.quantity,
                                      product.unitType,
                                    )}
                                  </Text>
                                ),
                              )}
                            </View>
                          )}

                          <TouchableOpacity
                            style={styles.expandedSkipButton}
                            onPress={(e) => {
                              e.stopPropagation();
                              handleSkip(user.id);
                            }}>
                            <Text style={styles.skipButtonText}>Skip</Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>

          <Sidebar
            visible={sidebarVisible}
            onClose={handleCloseSidebar}
            onDeliveryBoyPress={() => {
              setDeliveryBoyModalVisible(true);
              setSidebarVisible(false);
            }}
            onCustomerListPress={navigateToCustomerList}
            onDeliveryListPress={navigateToDeliveryList}
            onChooseRegionPress={navigateToChooseRegion}
            onPhoneInputPress={navigateToPhoneInputScreen}
            hasStarted={hasStarted}
          />

          {/* <DeliveryBoyModal
          visible={deliveryBoyModalVisible}
          onClose={() => setDeliveryBoyModalVisible(false)}
          onSelectDeliveryBoy={handleSelectDeliveryBoy}
        /> */}
                  
                    
				  <ImageCarouselModal
            visible={imageModalVisible}
            images={selectedImages}
            onClose={() => setImageModalVisible(false)}
          />

          <RegionModal
            visible={regionModalVisible}
            onClose={() => setRegionModalVisible(false)}
            onSelectRegion={handleSelectRegion}
          />
          <AllDeliveriesCompletedModal
            visible={allDeliveriesCompletedModalVisible}
            onClose={() => setAllDeliveriesCompletedModalVisible(false)}
            onReturnToMap={handleReturnToMap}
          />
          {showStartMessage && (
            <Animated.View style={[styles.startOverlay, {opacity: fadeAnim}]}>
              <Text style={styles.startText}>{displayText}</Text>
            </Animated.View>
          )}
        
        </View>
      </ScrollView>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundGreen,
  },
  map: {
    position: 'absolute',
    top: 56,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    flex: 1,
  },
  carouselContainer: {
    position: 'absolute',
    bottom: 50,
    width: '100%',
    maxHeight: '30%',
    backgroundColor: 'transparent',
    padding: 5,
    zIndex: 1,
  },
  singleContainer: {
    position: 'absolute',
    bottom: 50,
    width: '100%',
    maxHeight: '30%',
    backgroundColor: 'transparent',
    padding: 10,
    zIndex: 1,
    alignItems: 'center',
  },
  singleItem: {
    width: '95%',
    minHeight: 120,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    position: 'relative',
  },
  singleItemImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginRight: 15,
  },
  singleItemTextContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 5,
  },
  singleItemName: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#000',
    marginBottom: 5,
  },
  carouselItem: {
    // width: Dimensions.get('window').width,
    width: '20%',
// flex:1 ,
    height: "100%",
    marginRight: 10,
    // padding: 5,
    backgroundColor: '#fff',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
    position: 'relative',
  },
  carouselItemBeforeStart: {
    height: '70%',
    padding: 5,
    backgroundColor: '#eee',
  },
  carouselImage: {
    width: 45,
    height: 45,
    borderRadius: 8,
    marginRight: 8,
    // backgroundColor :"red"
  },
  carouselTextContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    marginTop: 5,
    // backgroundColor :"red"
  },
  carouselName: {
    fontWeight: 'bold',
    fontSize: 11,
    color: '#000',
  },
  carouselMilk: {
    fontSize: 9,
    color: '#000',
    marginTop: 2,
  },
  deliveredButton: {
    backgroundColor: '#28c76f',
    maxHeight: 70,
    paddingVertical: 3,
    paddingHorizontal: 6,
    justifyContent: 'center',
    borderRadius: 5,
    width: 60,
  },
  deliveredButtonDisabled: {
    backgroundColor: '#ccc',
  },
  completedButton: {
    backgroundColor: 'orange',
  },

  completedButtonText: {
    color: 'black',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 12,
  },

  deliveredButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  allDeliveredContainer: {
    position: 'absolute',
    bottom: 75,
    width: '100%',
    alignItems: 'center',
    zIndex: 1,
  },
  allDeliveredText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 10,
    borderRadius: 5,
  },
  errorButton: {
    backgroundColor: '#28c76f',
    paddingVertical: 10,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 10,
  },
  errorButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  marker: {
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 5,
    borderRadius: 6,
    borderColor: '#000',
    elevation: 2,
  },
  markerImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  markerText: {
    fontSize: 12,
    marginTop: 4,
  },
  startOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  startText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#28c76f',
  },
  countdownText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#28c76f',
    marginTop: 20,
  },
  deliveredCarouselItem: {
    backgroundColor: Colors.lightGreen,
    borderWidth: 1,
    borderColor: Colors.greenColour,
    opacity: 0.8,
  },
  selectedCarouselItem: {
    backgroundColor: Colors.BackgroundGreen,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  upcomingCarouselItem: {
    backgroundColor: Colors.greyBackground,
    borderWidth: 1,
    borderColor: Colors.listSeconary,
  },
  disabledCarouselItem: {
    backgroundColor: '#f0f0f0',
  },
  productInfo: {
    marginTop: 4,
  },
  productText: {
    fontSize: 12,
    color: Colors.listSeconary || '#666',
    marginBottom: 2,
  },
  addressText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  currentLocationButton: {
    position: 'absolute',
    bottom: 200,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 30,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  currentLocationIcon: {
    width: 24,
    height: 24,
  },
  // New horizontal carousel styles
  horizontalCarouselItem: {
    width: width - 40, // Full width minus padding
    height: "auto",
    marginHorizontal: 10,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    position: 'relative',
  },
  expandedCarouselItem: {
    height: 300, // Expanded height
  },
  // Collapsed view styles
  collapsedContainer: {
    flex: 1,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  collapsedImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginBottom: 10,
  },
  collapsedName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 15,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  deliverButton: {
    backgroundColor: '#28c76f',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  deliverButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  skipButton: {
    backgroundColor: '#ff6b6b',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  skipButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  // Expanded view styles
  expandedContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  expandedImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  expandedImage: {
    width: '90%',
    height: '90%',
    borderRadius: 8,
  },
  expandedDetailsContainer: {
    flex: 1,
    padding: 15,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    position: 'relative',
  },
  expandedName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
  },
  expandedSkipButton: {
    position: 'absolute',
    bottom: 15,
    right: 15,
    backgroundColor: '#ff6b6b',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
});

export default MapScreen;
