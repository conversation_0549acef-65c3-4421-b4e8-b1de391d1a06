import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Row from './CustomerRow';
import {Colors} from '../../constants/colors';
import {Customer} from '.';
import DraggableFlatList, {
  RenderItemParams,
  ScaleDecorator,
} from 'react-native-draggable-flatlist';

interface ListProps {
  customers: Customer[];
  onEditCustomer: (customerId: string) => void;
  onUpdateCustomerList: (customers: Customer[]) => void;
  isLoading: boolean;
  enableDrag: boolean;
}

const CustomerList: React.FC<ListProps> = ({
  customers,
  onEditCustomer,
  isLoading,
  onUpdateCustomerList,
  enableDrag,
}) => {
  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (customers.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.emptyText}>No customers found</Text>
      </View>
    );
  }

  const renderItem = ({item, drag}: RenderItemParams<any>) => (
    <ScaleDecorator>
      <TouchableOpacity
        activeOpacity={1}
        onLongPress={() => {
          if (enableDrag) {
            drag();
          }
        }}
        onPress={() => onEditCustomer?.(item)}>
        <Row customer={item} enableDrag={enableDrag} />
      </TouchableOpacity>
    </ScaleDecorator>
  );

  return (
    <DraggableFlatList
      data={customers}
      keyExtractor={item => item.customerId}
      onDragEnd={({data}) => {
        onUpdateCustomerList(data);
      }}
      renderItem={renderItem}
      contentContainerStyle={styles.listContent}
      dragItemOverflow={false}
    />
  );
};

const styles = StyleSheet.create({
  listContent: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 150,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  separator: {
    height: 1,
    marginHorizontal: 16,
  },
});

export default CustomerList;
