import {createAsyncThunk} from '@reduxjs/toolkit';
import axios from 'axios';
import {BASE_URL} from '../../constants/colors';
import { ACTIVE_API_BASE_URL } from '../../constants/api';

// Create the async thunk for adding a customer
export const addCustomer = createAsyncThunk(
  'customers/addCustomer',
  async (
    {
      regionId,
      customerData,
    }: {
      regionId: string;
      customerData: any;
    },
    {rejectWithValue},
  ) => {
    try {
      const response = await axios.post(
        `${ACTIVE_API_BASE_URL}/api/listings/add/${regionId}`,
        customerData,
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(error.response?.data || error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);
