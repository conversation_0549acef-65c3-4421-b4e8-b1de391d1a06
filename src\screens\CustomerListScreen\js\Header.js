// import React, {useState} from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   TouchableOpacity,
//   Image,
//   TextInput,
// } from 'react-native';
// import {Colors} from '../../../constants/colors';
// import {imagePath} from '../../../constants/imagePath';
// import {useNavigation} from '@react-navigation/native';

// const Header = ({title, onAddPress, onSearch}) => {
//   const navigation = useNavigation();
//   const [showSearchInput, setShowSearchInput] = useState(false);
//   const [searchQuery, setSearchQuery] = useState('');

//   const handleBackPress = () => {
//     navigation.goBack();
//   };

//   const toggleSearchInput = () => {
//     setShowSearchInput(!showSearchInput);
//     if (showSearchInput) {
//       setSearchQuery('');
//       onSearch?.('');
//     }
//   };

//   const handleSearch = text => {
//     setSearchQuery(text);
//     onSearch?.(text);
//   };

//   return (
//     <View style={styles.container}>
//       <View style={styles.leftContainer}>
//         <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
//           <Image source={imagePath.back_arrow_icon} style={styles.icon} />
//         </TouchableOpacity>
//       </View>

//       {showSearchInput ? (
//         <View style={styles.searchContainer}>
//           <TextInput
//             style={styles.searchInput}
//             placeholder="Search customers..."
//             value={searchQuery}
//             onChangeText={handleSearch}
//             autoFocus
//             placeholderTextColor={Colors.grey}
//           />
//         </View>
//       ) : (
//         <Text style={styles.title}>{title}</Text>
//       )}

//       <View style={styles.rightContainer}>
//         <TouchableOpacity onPress={toggleSearchInput} style={styles.iconButton}>
//           <Image source={imagePath.search_Icon} style={styles.icon} />
//         </TouchableOpacity>
//       </View>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     height: 56,
//     paddingHorizontal: 16,
//     backgroundColor: Colors.primary,
//     elevation: 4,
//     shadowColor: Colors.black,
//     shadowOffset: {width: 0, height: 2},
//     shadowOpacity: 0.2,
//     shadowRadius: 2,
//   },
//   leftContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   rightContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   title: {
//     flex: 1,
//     fontSize: 20,
//     fontWeight: 'bold',
//     color: Colors.white,
//     textAlign: 'center',
//   },
//   iconButton: {
//     padding: 8,
//   },
//   icon: {
//     width: 24,
//     height: 24,
//     tintColor: Colors.white,
//   },
//   searchContainer: {
//     flex: 1,
//     height: 40,
//     backgroundColor: Colors.white,
//     borderRadius: 20,
//     paddingHorizontal: 16,
//     marginHorizontal: 8,
//   },
//   searchInput: {
//     flex: 1,
//     fontSize: 16,
//     color: Colors.black,
//   },
// });

// export default Header;
