import React, { useState, useEffect } from 'react';
import {
  Text,
  StyleSheet,
  View,
  FlatList,
  RefreshControl,
} from 'react-native';
import { Colors } from '../../constants/colors';
import DeliveryListItem from './DeliveryListItem';

interface DeliveryListProps {
  deliveries: any[];
  onSelectDelivery: (delivery: any) => void;
  onEditDelivery?: (id: string) => void;
  onDeleteDelivery?: (id: string) => void;
}

const DeliveryList = ({
  deliveries,
  onSelectDelivery,
  onEditDelivery,
  onDeleteDelivery,
}: DeliveryListProps): React.JSX.Element => {
  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState(deliveries);

  useEffect(() => {
    setData(deliveries);
  }, [deliveries]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate fetching data
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const renderItem = ({ item }: { item: any }) => (
    <DeliveryListItem
      item={item}
      onPress={() => onSelectDelivery(item)}
      onEdit={() => onEditDelivery?.(item.id)}
      onDelete={() => onDeleteDelivery?.(item.id)}
    />
  );

  return (
    <>
      {data.length === 0 ? (
        <Text style={styles.emptyText}>No deliveries found</Text>
      ) : (
        <FlatList
          data={data}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderItem}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  listContent: {
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: Colors.listSeconary,
    marginTop: 40,
  },
});

export default DeliveryList;