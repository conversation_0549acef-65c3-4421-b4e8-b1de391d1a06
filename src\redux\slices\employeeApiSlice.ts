import { createSlice } from '@reduxjs/toolkit';
import { 
  fetchEmployeesApi, 
  fetchEmployeeByIdApi, 
  addEmployeeApi 
} from '../thunks/employeeApiThunks';
import { RootState } from '../store';

// Define a type for the employee
interface Employee {
  id: string;
  name: string;
  email: string;
  phoneNumber: string;
  // Add other properties as needed
}

interface EmployeeApiState {
  data: Employee[];
  selectedEmployee: Employee | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  currentOperation: 'fetch' | 'fetchById' | 'add' | null;
}

const initialState: EmployeeApiState = {
  data: [],
  selectedEmployee: null,
  status: 'idle',
  error: null,
  currentOperation: null,
};

const employeeApiSlice = createSlice({
  name: 'employeeA<PERSON>',
  initialState,
  reducers: {
    resetEmployeeApiStatus: state => {
      state.status = 'idle';
      state.error = null;
      state.currentOperation = null;
    },
  },
  extraReducers: builder => {
    // Handle fetchEmployeesApi
    builder
      .addCase(fetchEmployeesApi.pending, state => {
        state.status = 'loading';
        state.currentOperation = 'fetch';
      })
      .addCase(fetchEmployeesApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = action.payload.data || action.payload;
        state.error = null;
        state.currentOperation = 'fetch';
      })
      .addCase(fetchEmployeesApi.rejected, (state, action) => {
        console.log(action, "erirodf")
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'fetch';
      });

    // Handle fetchEmployeeByIdApi
    builder
      .addCase(fetchEmployeeByIdApi.pending, state => {
        state.status = 'loading';
        state.currentOperation = 'fetchById';
      })
      .addCase(fetchEmployeeByIdApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.selectedEmployee = action.payload.data || action.payload;
        state.error = null;
        state.currentOperation = 'fetchById';
      })
      .addCase(fetchEmployeeByIdApi.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'fetchById';
      });

    // Handle addEmployeeApi
    builder
      .addCase(addEmployeeApi.pending, state => {
        state.status = 'loading';
        state.currentOperation = 'add';
      })
      .addCase(addEmployeeApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data.push(action.payload.data || action.payload);
        state.error = null;
        state.currentOperation = 'add';
      })
      .addCase(addEmployeeApi.rejected, (state, action) => {
        console.log(action, "sdkjfdjf")
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'add';
      });
  },
});

export const { resetEmployeeApiStatus } = employeeApiSlice.actions;
export const employeeApiSelector = (state: RootState) => state.employeeApi;
export default employeeApiSlice.reducer;
