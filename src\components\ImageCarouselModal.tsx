import React, { useState } from 'react';
import { 
  Modal, 
  View, 
  Image, 
  StyleSheet, 
  Dimensions, 
  TouchableOpacity, 
  Text,
  FlatList
} from 'react-native';
import { Colors } from '../constants/colors';

interface ImageCarouselModalProps {
  visible: boolean;
  images: string[];
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

const ImageCarouselModal = ({ visible, images, onClose }: ImageCarouselModalProps) => {
  const [activeIndex, setActiveIndex] = useState(0);
  
  const renderItem = ({ item, index }: { item: string, index: number }) => (
    <View style={styles.slideContainer}>
      <Image 
        source={{ uri: item }} 
        style={styles.image}
        resizeMode="contain"
      />
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
        
        <FlatList
          data={images}
          renderItem={renderItem}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const newIndex = Math.floor(event.nativeEvent.contentOffset.x / width);
            setActiveIndex(newIndex);
          }}
          keyExtractor={(_, index) => index.toString()}
        />
        
        <View style={styles.pagination}>
          {images.map((_, index) => (
            <View 
              key={index} 
              style={[
                styles.paginationDot, 
                index === activeIndex && styles.paginationDotActive
              ]} 
            />
          ))}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideContainer: {
    width,
    height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: width * 0.9,
    height: height * 0.7,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    padding: 10,
  },
  closeButtonText: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: 'bold',
  },
  pagination: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  paginationDotActive: {
    backgroundColor: Colors.white,
  },
});

export default ImageCarouselModal;