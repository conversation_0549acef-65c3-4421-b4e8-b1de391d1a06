import React, { useState } from 'react';
import {View, Text, TextInput, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {Colors} from '../../../constants/colors';
import {imagePath} from '../../../constants/imagePath';
import {FontFamily} from '../../../constants/fontFamily';

interface InputTextProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  keyboardType?: 'default' | 'number-pad' | 'email-address' | 'phone-pad';
  error?: string;
  touched?: boolean;
  icon?: any;
  onIconPress?: () => void;
  maxLength?: number;
  disabled?: boolean;
}

const InputText = ({
  label,
  value,
  onChangeText,
  placeholder,
  keyboardType = 'default',
  error,
  touched,
  icon,
  onIconPress,
  maxLength,
  disabled = false,
}: InputTextProps): React.JSX.Element => {
  const [isFocused, setIsFocused] = useState(false);
  const [showError, setShowError] = useState(false);
  
  const hasError = touched && error;
  console.log(hasError, "hasErrorsd")
  return (
    <View style={styles.container}>
      <View style={[
        styles.inputContainer, 
        hasError ? styles.inputError : null,
        isFocused ? styles.inputFocused : null,
        disabled ? styles.inputDisabled : null
      ]}>
        <Text style={[
          styles.label,
          (isFocused || value) ? styles.labelFloating : styles.labelPlaceholder,
          disabled ? styles.labelDisabled : null
        ]}>
          {label}
        </Text>
        <TextInput
          style={[styles.input, disabled ? styles.inputTextDisabled : null]}
          value={value}
          onChangeText={onChangeText}
          placeholder=""
          keyboardType={keyboardType}
          maxLength={maxLength}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
        />
        {hasError && (
          <TouchableOpacity 
            onPress={() => setShowError(!showError)}
            style={styles.errorIconContainer}
          >
            <Image 
              source={imagePath.error_Icon} 
              style={styles.errorIcon} 
            />
          </TouchableOpacity>
        )}
        {icon && (
          <TouchableOpacity onPress={onIconPress} style={styles.iconContainer} disabled={disabled}>
            <Image source={icon} style={[styles.icon, disabled ? styles.iconDisabled : null]} />
          </TouchableOpacity>
        )}
      </View>
      
      {hasError && showError ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    position: 'absolute',
    backgroundColor: Colors.white,
    paddingHorizontal: 4,
    fontSize: 10,
    fontFamily: FontFamily.REGULAR,
    color: Colors.listSeconary,
    left: 12,
  },
  labelFloating: {
    top: -9,
    fontSize: 12,
    color: Colors.primary,
  },
  labelPlaceholder: {
    top: 14,
    fontSize: 16,
    color: Colors.listSeconary,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    paddingTop: 8,
  },
  inputFocused: {
    borderColor: Colors.primary,
  },
  inputError: {
    borderColor: Colors.danger,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.dark,
    paddingTop: 8,
  },
  iconContainer: {
    padding: 8,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: Colors.listSeconary,
  },
  errorText: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4
  },
  inputDisabled: {
    backgroundColor: Colors.greyBackground,
  },
  inputTextDisabled: {
    color: "grey",
  },
  labelDisabled: {
    color: "grey",
  },
  iconDisabled: {
    tintColor: "grey",
  },
  errorIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.danger,
  },
  errorIconContainer: {
    padding: 8,
  },
  tooltipOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tooltipContainer: {
    backgroundColor: Colors.white,
    padding: 16,
    borderRadius: 8,
    maxWidth: '80%',
    borderWidth: 1,
    borderColor: Colors.danger,
  },
  tooltipText: {
    color: Colors.danger,
    fontSize: 14,
  },
});

export default InputText;

