// import {createSlice} from '@reduxjs/toolkit';
// import {
//   addCustomerApi,
//   deleteCustomerApi,
//   fetchCustomersApi,
//   updateCustomerApi,
// } from '../thunks/Create/CustomerApiThunks';

// // Define a type for the customer
// interface Customer {
//   id: string;
//   name: string;
//   mobile: string;
//   email?: string;
//   region?: string;
//   floorNumber?: string;
//   doorNumber?: string;
//   houseType?: string;
//   latitude?: string;
//   longitude?: string;
//   images?: string[];
//   customerProducts?: any[];
//   // Add other properties as needed
// }

// interface CustomerApiState {
//   data: Customer[];
//   status: 'idle' | 'loading' | 'succeeded' | 'failed';
//   error: string | null;
//   currentOperation: 'fetch' | 'add' | 'update' | 'delete' | null;
//   deleteStatus: boolean;
// }

// const initialState: CustomerApiState = {
//   data: [],
//   status: 'idle',
//   error: null,
//   currentOperation: null,
//   deleteStatus: false,
// };

// const customerApiSlice = createSlice({
//   name: 'customerApi',
//   initialState,
//   reducers: {
//     resetCustomerApiStatus: state => {
//       state.status = 'idle';
//       state.error = null;
//       state.currentOperation = null;
//     },
//   },
//   extraReducers: builder => {
//     // Handle fetchCustomersApi
//     builder
//       .addCase(fetchCustomersApi.pending, state => {
//         state.status = 'loading';
//         state.currentOperation = 'fetch';
//       })
//       .addCase(fetchCustomersApi.fulfilled, (state, action) => {
//         state.status = 'succeeded';
//         state.data = action.payload;
//         state.error = null;
//         state.currentOperation = 'fetch';
//       })
//       .addCase(fetchCustomersApi.rejected, (state, action) => {
//         state.status = 'failed';
//         state.error = action.payload as string;
//         state.currentOperation = 'fetch';
//       });

//     // Handle addCustomerApi
//     builder
//       .addCase(addCustomerApi.pending, state => {
//         state.status = 'loading';
//         state.currentOperation = 'add';
//       })
//       .addCase(addCustomerApi.fulfilled, (state, action) => {
//         state.status = 'succeeded';
//         state.data.push(action.payload);
//         state.error = null;
//         state.currentOperation = 'add';
//       })
//       .addCase(addCustomerApi.rejected, (state, action) => {
//         state.status = 'failed';
//         state.error = action.payload as string;
//         state.currentOperation = 'add';
//       });

//     // Handle updateCustomerApi
//     builder
//       .addCase(updateCustomerApi.pending, state => {
//         state.status = 'loading';
//         state.currentOperation = 'update';
//       })
//       .addCase(updateCustomerApi.fulfilled, (state, action) => {
//         state.status = 'succeeded';
//         const index = state.data.findIndex(
//           customer => customer.id === action.payload.id,
//         );
//         if (index !== -1) {
//           state.data[index] = action.payload;
//         }
//         state.error = null;
//         state.currentOperation = 'update';
//       })
//       .addCase(updateCustomerApi.rejected, (state, action) => {
//         state.status = 'failed';
//         state.error = action.payload as string;
//         state.currentOperation = 'update';
//       });

//     // Handle deleteCustomerApi
//     builder
//       .addCase(deleteCustomerApi.pending, state => {
//         state.status = 'loading';
//         state.currentOperation = 'delete';
//       })
//       .addCase(deleteCustomerApi.fulfilled, (state, action) => {
//         state.status = 'succeeded';
//         state.data = state.data.filter(
//           customer => customer.id !== action.payload,
//         );
//         state.error = null;
//         state.currentOperation = 'delete';
//       })
//       .addCase(deleteCustomerApi.rejected, (state, action) => {
//         state.status = 'failed';
//         state.error = action.payload as string;
//         state.currentOperation = 'delete';
//       });
//   },
// });

// export const {resetCustomerApiStatus} = customerApiSlice.actions;
// export default customerApiSlice.reducer;
