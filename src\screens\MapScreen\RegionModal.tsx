import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  Image,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {Colors} from '../../constants/colors';
import {useAppSelector} from '../../redux/hooks';
import {imagePath} from '../../constants/imagePath';
import ApiContext from '../../context/ApiContext';
import {regionSelector} from '../../redux/slices/regionsSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface RegionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectRegion: (regionId: string) => void;
}

const RegionModal = ({
  visible,
  onClose,
  onSelectRegion,
}: RegionModalProps): React.JSX.Element => {
  // const { data: regions, status, error } = useAppSelector((state) => state.regions);
  const {data: regions, status, error} = useAppSelector(regionSelector);
  console.log(regions, 'regions');
  const [searchQuery, setSearchQuery] = useState('');
  const {api} = useContext(ApiContext) || {};
  const [hasSelectedRegion, setHasSelectedRegion] = useState(false);

  const [employeeRegions, setEmployeeRegions] = useState<any[]>([]);

  useEffect(() => {
    const loadEmployeeStores = async () => {
      try {
        const stores = await AsyncStorage.getItem('employeeStores');
        const parsedStores = stores ? JSON.parse(stores) : [];
        const filtered = regions?.filter(region =>
          parsedStores.some(
            (store: {storeId: any}) => store.storeId === region.storeId,
          ),
        );
        console.log(parsedStores, filtered, 'dtasdfjksdlf');

        setEmployeeRegions(filtered || []);
      } catch (error) {
        console.error('Failed to load employee stores:', error);
      }
    };

    loadEmployeeStores();
  }, [regions]);
  useEffect(() => {
    const checkRegion = async () => {
      if (api) {
        const region = await api.getSelectedRegion();
        console.log('Regioddddn:', region);
        setHasSelectedRegion(!!region);
      }
    };

    checkRegion();
  }, [api, visible]);

  const filteredRegions = employeeRegions?.filter(region =>
    region.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );
  console.log(filteredRegions, 'filterRegion');

  const renderItem = ({item}: {item: any}) => (
    <TouchableOpacity
      style={styles.regionItem}
      onPress={() => onSelectRegion(item.storeId)}>
      <View style={styles.regionInfo}>
        <Text style={styles.regionName}>{item.name}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={hasSelectedRegion ? onClose : undefined}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.modalContent}>
              <View style={styles.header}>
                <Text style={styles.title}>Select Region</Text>
                {!hasSelectedRegion || (
                  <TouchableOpacity
                    onPress={onClose}
                    style={styles.closeButton}>
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.searchContainer}>
                <Image
                  source={imagePath.search_Icon}
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search region..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholderTextColor={Colors.listSeconary}
                />
              </View>

              {status === 'loading' ? (
                <ActivityIndicator
                  size="large"
                  color={Colors.primary}
                  style={styles.loader}
                />
              ) : status === 'failed' ? (
                <Text style={styles.errorText}>
                  Error:{' '}
                  {typeof error === 'string' ? error : JSON.stringify(error)}
                </Text>
              ) : (
                <FlatList
                  data={filteredRegions}
                  keyExtractor={item => item.storeId}
                  renderItem={renderItem}
                  contentContainerStyle={styles.listContent}
                  ListEmptyComponent={
                    <Text style={styles.emptyText}>
                      {searchQuery
                        ? 'No regions found matching your search'
                        : 'No regions available'}
                    </Text>
                  }
                />
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: Colors.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.white,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.white,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.greyBackground,
    margin: 10,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  searchIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.listSeconary,
  },
  searchInput: {
    flex: 1,
    height: 40,
    paddingHorizontal: 10,
    color: Colors.dark,
  },
  listContent: {
    padding: 10,
  },
  regionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.greyBackground,
  },
  regionInfo: {
    flex: 1,
  },
  regionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.dark,
  },
  loader: {
    padding: 20,
  },
  errorText: {
    padding: 20,
    color: Colors.danger,
    textAlign: 'center',
  },
  emptyText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.listSeconary,
  },
});

export default RegionModal;
