import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
  TextInput,
  ActivityIndicator,
  Keyboard,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useForm, Controller} from 'react-hook-form';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import {Colors} from '../../constants/colors';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import {
  addEmployeeApi,
  fetchEmployeeByIdApi,
} from '../../redux/thunks/employeeApiThunks';
import {resetEmployeeApiStatus} from '../../redux/slices/employeeApiSlice';
import {storeEmployeeData, storeEmployeeStores} from '../../utils/storage';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../../navigation/AppNavigator';
import {fetchRegions} from '../../redux/thunks/regionThunks';
import {
  regionSelector,
  resetRegionApiStatus,
} from '../../redux/slices/regionsSlice';
import Dropdown from './Component/DropDown';

interface DeliveryBoyBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  phoneNumber: string;
}

const schema = yup.object().shape({
  name: yup.string().min(5).required('Name is required'),
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits'),
  address: yup.string().required('Address is required'),
  regionId: yup.string().required('Region is required'),
  routeId: yup.string().required('Route is required'),
});

const DeliveryBoyBottomSheet: React.FC<DeliveryBoyBottomSheetProps> = ({
  visible,
  onClose,
  phoneNumber,
}) => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const {data, fetchLoad1, status} = useAppSelector(regionSelector);
  const [isLoading, setIsLoading] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  // console.log(data,fetchLoad,status, 'datasdkf');

  useEffect(() => {
    console.log(data, fetchLoad1, status, 'datasdkf');
  }, [data, fetchLoad1, status]);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: {errors},
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      phone: phoneNumber || '',
      address: '',
      regionId: '',
      routeId: '',
    },
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  useEffect(() => {
    dispatch(fetchRegions());
  }, []);

  useEffect(() => {
    if (fetchLoad1 === 'succeeded') {
      dispatch(resetRegionApiStatus());
    }
  }, [fetchLoad1]);

  useEffect(() => {
    if (phoneNumber) {
      reset(prev => ({...prev, phone: phoneNumber}));
    }
  }, [phoneNumber]);

  const onSubmit = async (data: any) => {
    // console.log(data, "skdjfdkjf")
    console.log(data, 'dsfjdsfjk');
    // return
    setIsLoading(true);
    try {
      const addResponse = await dispatch(
        addEmployeeApi({
          name: data.name,
          address1: data.address,
          mobile: data.phone,
          routeId: data.routeId,
          stores: [data.regionId],
        }),
      ).unwrap();

      dispatch(resetEmployeeApiStatus());

      if (addResponse?.success) {
        const fetchResponse = await dispatch(
          fetchEmployeeByIdApi(data.phone),
        ).unwrap();

        if (fetchResponse?.data) {
          const employee = fetchResponse.data;
          await storeEmployeeData(employee);
          await storeEmployeeStores(employee.employeeStores);
          navigation.replace('Map');
        }
      }
    } catch (error) {
      console.error('Failed to save or fetch employee:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log('isKeyboardVisible ::');
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  return (
    <Modal
      transparent
      visible={visible}
      animationType="slide"
      onRequestClose={handleClose}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{flex: 1}}>
          <View style={styles.overlay}>
            <Pressable style={styles.backdrop} onPress={handleClose} />
            <View style={styles.bottomSheet}>
              <View style={styles.header}>
                <Text style={styles.title}>Add Delivery Boy Details</Text>
                <TouchableOpacity
                  onPress={handleClose}
                  style={styles.closeButton}>
                  <Text style={styles.closeIcon}>✕</Text>
                </TouchableOpacity>
              </View>

              <Text style={styles.description}>
                Please provide delivery boy information for phone number:{' '}
                {phoneNumber}
              </Text>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Name *</Text>
                <Controller
                  control={control}
                  name="name"
                  render={({field: {onChange, value}}) => (
                    <TextInput
                      style={[styles.input, errors.name && styles.inputError]}
                      placeholder="Enter delivery boy name"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
                {errors.name && (
                  <Text style={styles.errorText}>{errors.name.message}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Phone Number *</Text>
                <Controller
                  control={control}
                  name="phone"
                  render={({field: {onChange, value}}) => (
                    <TextInput
                      style={[styles.input, errors.phone && styles.inputError]}
                      placeholder="Enter phone number"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="phone-pad"
                      maxLength={10}
                    />
                  )}
                />
                {errors.phone && (
                  <Text style={styles.errorText}>{errors.phone.message}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Address *</Text>
                <Controller
                  control={control}
                  name="address"
                  render={({field: {onChange, value}}) => (
                    <TextInput
                      style={[
                        styles.input,
                        errors.address && styles.inputError,
                      ]}
                      placeholder="Enter address"
                      value={value}
                      onChangeText={onChange}
                      // multiline
                    />
                  )}
                />
                {errors.address && (
                  <Text style={styles.errorText}>{errors.address.message}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="regionId"
                  render={({
                    field: {onChange, value},
                    fieldState: {error, isTouched},
                  }) => (
                    <Dropdown
                      label="Region & Route *"
                      value={value}
                      options={data.map(region => ({
                        label: region.name,
                        value: region.storeId,
                      }))}
                      onSelect={(selectedRouteId, selectedRegionId) => {
                        onChange(selectedRegionId);
                        setValue('routeId', selectedRouteId ?? '');
                      }}
                      error={error?.message}
                      touched={isTouched}
                    />
                  )}
                />

                {errors.regionId && (
                  <Text style={styles.errorText}>
                    {errors.regionId.message}
                  </Text>
                )}
              </View>
              {!isKeyboardVisible && (
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={handleSubmit(onSubmit)}
                  disabled={isLoading}>
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.saveButtonText}>Save</Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    //  maxHeight: '100%',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.dark,
  },
  closeButton: {
    padding: 5,
  },
  closeIcon: {
    fontSize: 20,
    color: Colors.listSeconary,
  },
  description: {
    fontSize: 14,
    color: Colors.listSeconary,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: Colors.dark,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    color: Colors.dark,
  },
  inputError: {
    borderColor: Colors.danger,
  },
  errorText: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 5,
  },
  saveButton: {
    backgroundColor: '#36A546',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 10,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    padding: 8,
  },
  dropdownItem: {
    paddingVertical: 6,
    paddingHorizontal: 8,
  },
  selectedItem: {
    backgroundColor: '#e0e0e0',
  },
});

export default DeliveryBoyBottomSheet;

function addDeliveryApi(deliveryData: {
  customerName: string;
  address: string;
  phoneNumber: string;
  status: string;
  date: string;
}): any {
  throw new Error('Function not implemented.');
}
