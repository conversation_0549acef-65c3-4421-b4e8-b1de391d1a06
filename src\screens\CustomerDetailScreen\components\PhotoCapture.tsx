import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image, ScrollView} from 'react-native';
import {Colors} from '../../../constants/colors';
import { FontFamily } from '../../../constants/fontFamily';
import { ACTIVE_API_BASE_URL, IMAGE_API_BASE_URL } from '../../../constants/api';
export interface Photo {
  uri: string;
  isNew: boolean;
  CustomerImageId?: string;
  imageName?:string
}
interface PhotoCaptureProps {
  photoUri: string | null;
  photos?: Photo[];
  onCapture: () => void;
  onDeletePhoto?: (index: number) => void;
  error?: string;
  touched?: boolean;
  customerCode?:string|null;
}

const PhotoCapture = ({
  photoUri,
  photos = [],
  onCapture,
  onDeletePhoto,
  error,
  touched,
  customerCode
}: PhotoCaptureProps): React.JSX.Element => {
  const isMaxPhotos = photos.length >= 3;
  
  // console.log("PhotoCapture - photos:", photos); 
  
  return (
    <View style={styles.container}>
      <Text style={styles.label}>Customer Photo ({photos.length}/3)</Text>
      
      {photos.length > 0 && (
        <View style={styles.photoContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {photos.map((photo, index) => {
              const imageUri = photo.isNew
                ? photo.uri
                : `${IMAGE_API_BASE_URL}/${customerCode}/${photo.imageName}`;
              
              return(
                <View key={index} style={styles.photoWrapper}>
                  <Image 
                    source={{ uri: imageUri }}
                    style={styles.photo} 
                    resizeMode="cover"
                  />
                  {onDeletePhoto && (
                    <TouchableOpacity 
                      style={styles.deleteButton}
                      onPress={() => onDeletePhoto(index)}
                    >
                      <Text style={styles.deleteButtonText}>✕</Text>
                    </TouchableOpacity>
                  )}
                </View>
              );
            })}
          </ScrollView>
        </View>
      )}
      
      <TouchableOpacity 
        style={[
          styles.captureButton,
          touched && error ? styles.captureButtonError : null,
          isMaxPhotos ? styles.disabledButton : null,
        ]}
        onPress={onCapture}
        disabled={isMaxPhotos}
      >
        <Text style={[
          styles.captureText,
          isMaxPhotos ? styles.disabledText : null
        ]}>
          {photos.length === 0 ? 'Take Photo' : isMaxPhotos ? 'Maximum Photos Reached' : 'Add Another Photo'}
        </Text>
      </TouchableOpacity>
      
      {touched && error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    // fontSize: 16,
    fontWeight: 'bold',
    color: Colors.dark,
    marginBottom: 8,
    fontSize: 14,
    fontFamily: FontFamily.REGULAR,
  },
  captureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.greyBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderStyle: 'dotted',
    marginTop: 8,
  },
  captureButtonError: {
    borderColor: Colors.danger,
  },
  disabledButton: {
    backgroundColor: '#f0f0f0',
    borderColor: '#e0e0e0',
  },
  disabledText: {
    color: '#999',
  },
  photoContainer: {
    marginBottom: 8,
  },
  photoWrapper: {
    borderWidth: 1,
    borderColor: Colors.greyBackground,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 8,
  },
  photo: {
    width: 150,
    height: 150,
  },
  captureText: {
    color: Colors.primary,
    fontWeight: 'bold',
    fontSize: 14,
    fontFamily: FontFamily.REGULAR,
  },
  errorText: {
    color: Colors.danger,
    fontSize: 12,
    marginTop: 4,
  },
  deleteButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    width: 22,
    height: 22,
    borderRadius: 11,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default PhotoCapture;

