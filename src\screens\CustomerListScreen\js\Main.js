// import React, { useState, useEffect, useContext } from 'react';
// import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
// import { useNavigation } from '@react-navigation/native';
// import { useAppDispatch, useAppSelector } from '../../../redux/hooks';
// import { fetchCustomersApi } from '../../../redux/thunks/fetchCustomersApiThunk';
// import Header from '../CustomerListHeader';
// import List from '../CustomerList';
// import { imagePath } from '../../../constants/imagePath';
// import { Colors } from '../../../constants/colors';
// import DeleteConfirmationModal from '../../../components/DeleteConfirmationModal';
// import ApiContext from '../../../context/ApiContext';
// import { deleteCustomerApi } from '../../../redux/thunks/customerApiThunks';

// const Main = () => {
//   const navigation = useNavigation();
//   const dispatch = useAppDispatch();
//   const { api } = useContext(ApiContext) || {};
//   const { data: customers, status } = useAppSelector(state => state.customerApi);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
//   const [customerToDelete, setCustomerToDelete] = useState(null);

//   useEffect(() => {
//     if (status === 'idle') {
//       dispatch(fetchCustomersApi());
//     }
//   }, [dispatch, status]);

//   const filteredCustomers = customers.filter(customer =>
//     customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
//     (customer.mobile && customer.mobile.includes(searchQuery))
//   );

//   const handleAddCustomer = () => {
//     navigation.navigate('CustomerDetail');
//   };

//   const handleSelectCustomer = (customer) => {
//     navigation.navigate('CustomerDetail', {
//       customerId: customer.id,
//       customerData: customer,
//     });
//   };

//   const handleEditCustomer = (customerId) => {
//     navigation.navigate('CustomerDetail', { customerId });
//   };

//   const handleDeleteCustomer = (customerId) => {
//     setCustomerToDelete(customerId);
//     setIsDeleteModalVisible(true);
//   };

//   const confirmDelete = () => {
//     if (customerToDelete) {
//       dispatch(deleteCustomerApi(customerToDelete));
//     }
//     setIsDeleteModalVisible(false);
//     setCustomerToDelete(null);
//   };

//   const cancelDelete = () => {
//     setIsDeleteModalVisible(false);
//     setCustomerToDelete(null);
//   };

//   const handleSearch = (query) => {
//     setSearchQuery(query);
//   };

//   return (
//     <View style={styles.container}>
//       <Header
//         title="Customer List"
//         onAddPress={handleAddCustomer}
//         onSearch={handleSearch}
//       />

//       <List
//         customers={filteredCustomers}
//         onSelectCustomer={handleSelectCustomer}
//         onEditCustomer={handleEditCustomer}
//         onDeleteCustomer={handleDeleteCustomer}
//         isLoading={status === 'loading'}
//       />

//       <TouchableOpacity style={styles.fabButton} onPress={handleAddCustomer}>
//         <Image source={imagePath.plus_Icon} style={styles.fabIcon} />
//       </TouchableOpacity>

//       <DeleteConfirmationModal
//         visible={isDeleteModalVisible}
//         onCancel={cancelDelete}
//         onConfirm={confirmDelete}
//         title="Delete Customer"
//         message="Are you sure you want to delete this customer?"
//       />
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: Colors.white,
//   },
//   fabButton: {
//     position: 'absolute',
//     bottom: 20,
//     right: 20,
//     width: 56,
//     height: 56,
//     borderRadius: 28,
//     backgroundColor: Colors.primary,
//     justifyContent: 'center',
//     alignItems: 'center',
//     elevation: 5,
//     shadowColor: Colors.black,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.3,
//     shadowRadius: 3,
//   },
//   fabIcon: {
//     width: 24,
//     height: 24,
//     tintColor: Colors.white,
//   },
// });

// export default Main;
