{"name": "SixAmDeliveryApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@mapbox/polyline": "^1.2.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "formik": "^2.4.6", "i18next-react-native-language-detector": "^1.0.2", "lottie-react-native": "^7.2.2", "polyline": "^0.2.0", "react": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "^4.0.2", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.25.0", "react-native-image-picker": "^8.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-localize": "^3.4.1", "react-native-maps": "^1.23.7", "react-native-maps-directions": "^1.9.0", "react-native-permissions": "^5.4.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-sim": "^0.1.0", "react-native-sim-cards-manager": "^1.0.27", "react-native-sim-data": "^3.0.0", "react-native-sms-retriever": "^1.1.1", "react-native-snap-carousel": "^1.3.1", "react-native-swipe-list-view": "^3.2.9", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}