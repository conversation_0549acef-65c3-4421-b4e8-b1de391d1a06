import React from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Colors } from '../../constants/colors';
import LottieView from 'lottie-react-native';
import { animationPath } from '../../constants/animationPath';

interface AllDeliveriesCompletedModalProps {
  visible: boolean;
  onClose: () => void;
  onReturnToMap: () => void;
}

const AllDeliveriesCompletedModal = ({
  visible,
  onClose,
  onReturnToMap,
}: AllDeliveriesCompletedModalProps): React.JSX.Element => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.animationContainer}>
            <LottieView
              source={animationPath.delivery_ride_animation}
              autoPlay
              loop={false}
              speed={0.5}
              style={styles.animation}
            />
          </View>
          
          <Text style={styles.title}>All Deliveries Completed!</Text>
          <Text style={styles.message}>
            Great job! You have successfully completed all deliveries for today.
          </Text>
          
          <TouchableOpacity
            style={styles.button}
            onPress={onReturnToMap}
          >
            <Text style={styles.buttonText}>Return to Map</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: Colors.white,
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    elevation: 5,
  },
  animationContainer: {
    width: 150,
    height: 150,
    marginBottom: 20,
  },
  animation: {
    width: '100%',
    height: '100%',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: Colors.dark,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    marginTop: 10,
  },
  buttonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AllDeliveriesCompletedModal;