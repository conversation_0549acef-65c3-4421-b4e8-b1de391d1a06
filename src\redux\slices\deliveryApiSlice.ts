import { createSlice } from '@reduxjs/toolkit';
import { 
  fetchDeliveriesApi, 
  addDeliveryApi, 
  updateDeliveryApi, 
  deleteDeliveryApi,
  updateDeliveryStatus, 
  startDeliveryApi
} from '../thunks/deliveryApiThunks';

// Define a type for the delivery
interface Delivery {
  id: string;
  customerId: string;
  customerName: string;
  status: 'Pending' | 'In Transit' | 'Delivered';
  address: string;
  date: string;
  // Add other properties as needed
}

interface DeliveryApiState {
  data: any;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  currentOperation: 'fetch' | 'add' | 'update' | 'delete' | null;
  startDel: 'idle' | 'loading' | 'succeeded' | 'failed';
  fetchLoad: 'idle' | 'loading' | 'succeeded' | 'failed';
  Routedata: any;
}

const initialState: DeliveryApiState = {
  data: [],
  status: 'idle',
  error: null,
  currentOperation: null,
  startDel:"idle",
  fetchLoad:"idle",
Routedata:[]
};

const deliveryApiSlice = createSlice({
  name: 'deliveryApi',
  initialState,
  reducers: {
    resetDeliveryApiStatus: (state) => {
      state.status = 'idle';
      state.error = null;
      state.currentOperation = null;
      state.startDel="idle"
      state.fetchLoad="idle"
    }
  },
  extraReducers: (builder) => {
    // Handle fetchDeliveriesApi
    builder
      .addCase(fetchDeliveriesApi.pending, (state) => {
        state.status = 'loading';
        state.currentOperation = 'fetch';
        state.fetchLoad = 'loading';
      })
      .addCase(fetchDeliveriesApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.fetchLoad = 'succeeded';
        state.data = action.payload;
        state.error = null;
        state.currentOperation = 'fetch';
      })
      .addCase(fetchDeliveriesApi.rejected, (state, action) => {
        state.status = 'failed';
        state.fetchLoad = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'fetch';
      });

    // Handle addDeliveryApi
    builder
      .addCase(addDeliveryApi.pending, (state) => {
        state.status = 'loading';
        state.currentOperation = 'add';
      })
      .addCase(addDeliveryApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data.push(action.payload);
        state.error = null;
        state.currentOperation = 'add';
      })
      .addCase(addDeliveryApi.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'add';
      });

    // Handle updateDeliveryApi
    builder
      .addCase(updateDeliveryApi.pending, (state) => {
        state.status = 'loading';
        state.currentOperation = 'update';
      })
      .addCase(updateDeliveryApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const index = state.data.findIndex(delivery => delivery.id === action.payload.id);
        if (index !== -1) {
          state.data[index] = action.payload;
        }
        state.error = null;
        state.currentOperation = 'update';
      })
      .addCase(updateDeliveryApi.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'update';
      });

    // Handle deleteDeliveryApi
    builder
      .addCase(deleteDeliveryApi.pending, (state) => {
        state.status = 'loading';
        state.currentOperation = 'delete';
      })
      .addCase(deleteDeliveryApi.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.data = state.data.filter(delivery => delivery.id !== action.payload);
        state.error = null;
        state.currentOperation = 'delete';
      })
      .addCase(deleteDeliveryApi.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
        state.currentOperation = 'delete';
      });

    // Handle updateDeliveryStatus
    builder
      .addCase(updateDeliveryStatus.pending, (state) => {
        state.status = 'loading';
        state.currentOperation = 'update';
        state.error = null;
      })
      .addCase(updateDeliveryStatus.fulfilled, (state, action) => {
        state.status = 'succeeded';
        // Update the delivery status in the state
        const updatedDelivery = action.payload.data;
        if (updatedDelivery && updatedDelivery.deliveryId) {
          const index = state.data.findIndex(
            (            delivery: { id: any; }) => delivery.id === updatedDelivery.deliveryId
          );
          if (index !== -1) {
            state.data[index].status = 'Delivered';
          }
        }
      })
      .addCase(updateDeliveryStatus.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string || 'Failed to update delivery status';
      });
       builder
      .addCase(startDeliveryApi.pending, (state) => {
        state.startDel = 'loading';
        state.currentOperation = 'update';
        state.error = null;
      })
      .addCase(startDeliveryApi.fulfilled, (state, action) => {
        state.startDel = 'succeeded';
        state.Routedata=action.payload?.errors
      })
      .addCase(startDeliveryApi.rejected, (state, action) => {
        state.startDel = 'failed';
        state.error = action.payload as string || 'Failed to update delivery status';
      });
  },
});

export const { resetDeliveryApiStatus } = deliveryApiSlice.actions;
export default deliveryApiSlice.reducer;
