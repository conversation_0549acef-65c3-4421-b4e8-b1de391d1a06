import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, Image, StyleSheet } from 'react-native';
import { Colors } from '../../../constants/colors';
import { imagePath } from '../../../constants/imagePath';
import { useAppDispatch, useAppSelector } from '../../../redux/hooks';
import { fetchProducts } from '../../../redux/thunks/productThunks';
import { productSelector } from '../../../redux/slices/productsSlice';

interface MilkSelectorProps {
  onMilkSelectionChange: any;
  initialSelected?: boolean;
  initialLiters?: number;
  initialMilliliters?: number;
  value:any;
}

const MilkSelector = ({
  onMilkSelectionChange,
  value,
  initialSelected = false,
  initialLiters = 0,
  initialMilliliters = 0.25,
}: MilkSelectorProps): React.JSX.Element => {
  interface ProductSelection {
  selected: boolean;
  value:any;
  liters: number;
  milliliters: number;
}
const [productSelections, setProductSelections] = useState<Record<string, ProductSelection>>({});
console.log(productSelections, value,"asfsdfd")
  const dispatch=useAppDispatch();
  const {productData}=useAppSelector(productSelector);

const updateLiters = (productId: string, delta: number) => {
   setProductSelections((prev) => {
    const existing = prev[productId] || {
      selected: true,
      liters: 0,
      milliliters: 0.25,
    };

    const updated = {
      ...prev,
      [productId]: {
        ...existing,
        liters: Math.max(0, existing.liters + delta),
      },
    };

    onMilkSelectionChange(updated);

    return updated;
  });

};

const updateMilliliters = (productId: string, delta: number) => {
  setProductSelections((prev) => {
    let { liters, milliliters } = prev[productId];
    milliliters += delta;

    if (milliliters >= 1) {
      liters += 1;
      milliliters = 0;
    } else if (milliliters < 0) {
      if (liters > 0) {
        liters -= 1;
        milliliters = 0.75;
      } else {
        milliliters = 0.25; 
      }
    }

      const updated = {
      ...prev,
      [productId]: { ...prev[productId], liters, milliliters },
    };

    onMilkSelectionChange(updated);

    return updated;
  });
};

const toggleProductSelection = (productId: string) => {
  setProductSelections((prev) => {
    let updatedSelections;
    const existing = prev[productId];

    if (existing?.selected) {
      updatedSelections = { ...prev };
      delete updatedSelections[productId];
    } else {
      updatedSelections = {
        ...prev,
        [productId]: {
          selected: true,
          liters: 0,
          milliliters: 0.25,
        },
      };
    }

    onMilkSelectionChange(updatedSelections);

    return updatedSelections;
  });
};


useEffect(()=>{
dispatch(fetchProducts())
},[])
useEffect(() => {
  if (value?.length && productData?.length) {
    const initialSelections: Record<string, ProductSelection> = {};

    value.forEach((item: { quantity: number; productId: string | number; }) => {
      const liters = Math.floor(item.quantity);
      const milliliters = item.quantity % 1;

      initialSelections[item.productId] = {
        selected: true,
        value: item,
        liters,
        milliliters,
      };
    });

    setProductSelections(initialSelections);
    onMilkSelectionChange(initialSelections);
  }
}, [value, productData]);

  return (
    <View>
  {productData?.map((prod) => {
  const selection = productSelections[prod.id];
  const isSelected = !!selection?.selected;
  const liters = selection?.liters ?? 0;
  const milliliters = selection?.milliliters ?? 0.25;

  return (
    <View
      key={prod.id}
      style={[styles.container, { width: !isSelected ? '30%' : '100%' }]}
    >
      <TouchableOpacity
        onPress={() => toggleProductSelection(prod.id)}
        style={styles.selectionButton}
      >
        <View style={styles.radioButton}>
          {isSelected && <View style={styles.radioButtonSelected} />}
        </View>
        <Text style={styles.labelText}>{prod.name}</Text>
      </TouchableOpacity>

      {isSelected && (
        <View style={styles.quantityContainer}>
          <View style={styles.counterContainer}>
            <TouchableOpacity onPress={() => updateLiters(prod.id, 1)} style={styles.arrowButton}>
              <Image source={imagePath.arrowUp_Icon} style={styles.arrowIcon} />
            </TouchableOpacity>

            <TextInput
              style={styles.quantityInput}
              value={String(liters)}
              keyboardType="numeric"
              editable={false}
            />

            <TouchableOpacity onPress={() => updateLiters(prod.id, -1)} style={styles.arrowButton}>
              <Image source={imagePath.arrowDown_Icon} style={styles.arrowIcon} />
            </TouchableOpacity>
          </View>

          <Text style={styles.unitLabelMl}>L:</Text>
          <View style={styles.counterContainer}>
            <TouchableOpacity onPress={() => updateMilliliters(prod.id, 0.25)} style={styles.arrowButton}>
              <Image source={imagePath.arrowUp_Icon} style={styles.arrowIcon} />
            </TouchableOpacity>

            <TextInput
              style={styles.quantityInput}
              value={String(milliliters)}
              keyboardType="numeric"
              editable={false}
            />

            <TouchableOpacity
              onPress={() => updateMilliliters(prod.id, -0.25)}
              disabled={liters === 0 && milliliters === 0.25}
              style={[styles.arrowButton, { opacity: liters === 0 && milliliters === 0.25 ? 0.3 : 1 }]}
            >
              <Image source={imagePath.arrowDown_Icon} style={styles.arrowIcon} />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
})}

</View>

  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
    backgroundColor: 'white',
    borderColor: '#c8c8c8',
    justifyContent: 'space-between',
  },
  selectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#6C8F25',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  radioButtonSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#6C8F25',
  },
  labelText: {
    color: '#000',
    fontSize: 16,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  unitLabel: {
    fontSize: 16,
    marginRight: 5,
    color: Colors.black,
  },
  unitLabelMl: {
    fontSize: 16,
    marginLeft: 10,
    marginRight: 5,
    color: Colors.black,
  },
  counterContainer: {
    alignItems: 'center',
  },
  arrowButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowIcon: {
    width: 25,
    height: 25,
    tintColor: 'black',
    resizeMode: 'contain',
  },
  quantityInput: {
    borderRadius: 3,
    borderWidth: 1,
    borderColor: '#c8c8c8',
    width: 40,
    height: 40,
    textAlign: 'center',
    marginVertical: 5,
    color: '#000',
  },
});

export default MilkSelector;