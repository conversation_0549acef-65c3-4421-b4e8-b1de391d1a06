import React, {useEffect, useContext, useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Text,
  Alert,
  Image,
  ActivityIndicator,
  Platform,
  TextInput,
  Linking,
  Modal,
  Pressable,
} from 'react-native';
import {Colors} from '../../constants/colors';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import * as Yup from 'yup';
import ApiContext from '../../context/ApiContext';
import Header from './components/Header';
import InputText from './components/InputText';
import Dropdown from './components/Dropdown';
import PhotoCapture from './components/PhotoCapture';
import MilkSelector from './components/MilkSelector';
import {useRoute, RouteProp, useNavigation} from '@react-navigation/native';
import {RootStackParamList} from '../../navigation/AppNavigator';
import {launchCamera} from 'react-native-image-picker';
import Geolocation from '@react-native-community/geolocation';
import {imagePath} from '../../constants/imagePath';
import {resetCustomerStatus} from '../../redux/slices/customersSlice';
import {combineSlices} from '@reduxjs/toolkit';
import {PermissionsAndroid} from 'react-native';
import {FontFamily} from '../../constants/fontFamily';
type CustomerDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'CustomerDetail'
>;
import RNFS from 'react-native-fs';
import {
  addCustomerApi,
  updateCustomerApi,
  uploadCustomerImagesApi,
} from '../../redux/thunks/Create/CustomerApiThunks';
import {
  customerSelector,
  resetCustomerApiStatus,
  resetCustomerData,
} from '../../redux/slices/Create/customerApiSlice';
import {useForm, Controller} from 'react-hook-form';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import CustomInputController from './components/CustomInputController';
import {IMAGE_API_BASE_URL} from '../../constants/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {fetchRotes} from '../../redux/thunks/regionThunks';
import {
  regionSelector,
  resetRegionApiStatus,
} from '../../redux/slices/regionsSlice';

const CustomerDetailScreen = (): React.JSX.Element => {
  interface EmptData {
    employeeId: string;
    name: string;
    mobile: number;
    address1: string;
    address2: string;
    cityId: string;
    stateId: string;
    countryId: string;
    postalCodeId: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    employeeStores: EmployeeStore[];
    employeeRoutes: EmployeeRoutes[];
  }

  interface EmployeeStore {
    storeId: string;
    storeName: string;
  }
  interface EmployeeRoutes {
    id: string;
    name: string;
  }
  const route = useRoute<CustomerDetailScreenRouteProp>();
  const navigation = useNavigation();
  const customerData = route?.params?.customerData ?? null;
  const customerId = customerData?.customerId ?? null;
  console.log('123:', customerId, customerData);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const viewOnly = route.params?.viewOnly || false;
  // Use customerData to populate form if available
  const {routeData, fetchLoad} = useAppSelector(regionSelector);
  console.log(routeData, 'jrsldkf');
  useEffect(() => {
    if (customerData) {
      reset({
        region: selectedRegion ? selectedRegion.name : customerData?.region,
        location: 'Location',
        houseType: customerData.housetype || '',
        floorNumber: customerData.floorNumber || '',
        doorNumber: customerData.doorNumber || '',
        name: customerData.name || '',
        phoneNumber: customerData.mobile?.toString() || '',
        // email: customerData.email || '',
        // photo:
        //   customerData.pictures && customerData.pictures.length > 0
        //     ? customerData.pictures[0]
        //     : null,
      });

      setCustomerProd(customerData?.customerProducts);

      if (customerData.images && customerData.images.length > 0) {
        const base64Images = customerData.images.map(
          (img: {imageContent: string; customerImageId: string}) => ({
            ...img,
            uri: `data:image/jpeg;base64,${img.imageContent}`,
            customerImageId: img.customerImageId,
            isNew: false,
          }),
        );

        setPhotos(customerData?.images);
        setPhotoUri(customerData[0]?.uri ?? null);
        setcustomerCode(customerData?.customerCode);
      }

      if (customerData.milkData) {
        setIsMilkSelected(customerData.milkData.selected || false);
        setMilkLiters(customerData.milkData.liters || 0);
        setMilkMilliliters(customerData.milkData.milliliters || 0.25);
      }
    }
  }, [customerData]);
  const fetchAddressFromCoords = async () => {
    if (customerData.latitude && customerData.longitude) {
      try {
        const address = await api?.getAddressFromCoordinates(
          customerData.latitude,
          customerData.longitude,
        );

        setFetchedAddress(address);
        setLocation({
          latitude: customerData.latitude,
          longitude: customerData.longitude,
        });
        setIsEditMode(false);
        setButtonText('Location fetched');
      } catch (error) {
        console.log('Failed to fetch address from coordinates:', error);
        api?.showToast('Unable to fetch address from coordinates');
      }
    }
  };
  useEffect(() => {
    fetchAddressFromCoords();
  }, [customerData]);

  const schema = yup.object().shape({
    region: yup.string().required('Region is required'),
    location: yup.string().required('Location is required'),
    houseType: yup.string().notRequired(),
    floorNumber: yup.string().when('houseType', {
      is: 'apartment',
      then: schema => schema.required('Floor number is required'),
      otherwise: schema => schema.notRequired(),
    }),
    doorNumber: yup.string().when('houseType', {
      is: (val: string) => ['apartment', 'individual'].includes(val),
      then: schema => schema.required('Door number is required'),
      otherwise: schema => schema.notRequired(),
    }),
    name: yup.string().required('Name is required'),
    phoneNumber: yup
      .string()
      .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
      .required('Phone number is required'),
    customerProdValid: yup
      .boolean()
      .oneOf([true], 'Please select at least one product'),
  });

  const {
    control,
    handleSubmit,
    register,
    watch,
    setValue,
    reset,
    formState: {errors},
  } = useForm({
    defaultValues: {
      region: '',
      location: '',
      houseType: '',
      floorNumber: '',
      doorNumber: '',
      name: '',
      phoneNumber: '',
      customerProdValid: false,
    },
    resolver: yupResolver(schema),
  });
  console.log(errors, 'erordslkjf;');

  const dispatch = useAppDispatch();
  const {api} = useContext(ApiContext) || {};
  const [photoUri, setPhotoUri] = useState<string | null>(null);
  const [customerCode, setcustomerCode] = useState<string | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [photos, setPhotos] = useState<
    Array<{
      uri: string;
      isNew: boolean;
      CustomerImageId?: string;
      fileName: string;
      type: string;
      imageName?: string;
      imageType?: string;
    }>
  >([]);
  console.log(photos, 'photos');
  const [isFetchingLocation, setIsFetchingLocation] = useState<boolean>(false);
  const [buttonText, setButtonText] = useState<string>('Get Location');
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [isMilkSelected, setIsMilkSelected] = useState(false);
  const [milkLiters, setMilkLiters] = useState(0);
  const [milkMilliliters, setMilkMilliliters] = useState(0.25);
  const [empData, setempData] = useState<EmptData>();

  console.log(location, empData, 'formValues');
  const [customerProd, setCustomerProd] = useState([]);
  // Get customer status from Redux
  const customerState = useAppSelector(state => state.customers);

  const {currentOperation, status, error, operationStatus} =
    useAppSelector(customerSelector);
  // const status = customerState ? customerState.status : 'idle';
  // const error = customerState ? customerState.error : null;
  console.log(error, 'eriedfkjd');
  console.log(
    status,
    error,
    currentOperation,
    photos,
    photoUri,
    'StatusCustomer',
  );
  console.log(operationStatus.update, 'StatusCustosdfsdfmer');
  useEffect(() => {
    // return () => {
    dispatch(resetCustomerApiStatus());
    // };
  }, []);

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        console.log('Requesting location permission...');
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        console.log('Permission result:', granted);

        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert(
            'Permission Denied',
            'You need to grant location permission.',
          );
          return false;
        }
      }
      return true;
    } catch (err) {
      console.error('Error requesting location permission:', err);
      return false;
    }
  };
  const [fetchedAddress, setFetchedAddress] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);

  const checkGPSStatus = async () => {
    return new Promise(resolve => {
      Geolocation.getCurrentPosition(
        () => resolve(true),
        error => {
          if (error.code === 2) {
            resolve(false);
          } else {
            resolve(true);
          }
        },
        {enableHighAccuracy: true, timeout: 5000, maximumAge: 1000},
      );
    });
  };
  console.log(fetchedAddress, 'fetchadre');
  const fetchLocation = async () => {
    setValue('location', 'Location', {shouldValidate: true});
    console.log('Fetching location...');
    setButtonText('Loading...');

    try {
      setIsFetchingLocation(true);

      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'Please enable location permission.',
        );
        setButtonText('Locate Me');
        setIsFetchingLocation(false);
        return;
      }

      const isGPSEnabled = await checkGPSStatus();
      if (!isGPSEnabled) {
        Alert.alert(
          'GPS Disabled',
          'Please turn on your GPS to fetch location.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Open Settings',
              onPress: () => Linking.openSettings(),
            },
          ],
        );
        setButtonText('Locate Me');
        setIsFetchingLocation(false);
        return;
      }

      Geolocation.getCurrentPosition(
        async position => {
          const {latitude, longitude} = position.coords;
          setLocation(position.coords);

          try {
            const address = await api?.getAddressFromCoordinates(
              latitude,
              longitude,
            );
            setFetchedAddress(address);
            setValue('location', address, {shouldValidate: true});
            setIsEditMode(false);
            api?.showToast('Location fetched successfully!');
            setButtonText('Location fetched');
          } catch (err) {
            console.log('Address fetch error:', err);
            api?.showToast('Failed to fetch address');
            setFetchedAddress('');
          }
        },
        error => {
          console.log('Location fetch error:', error);
          api?.showToast('Could not get current position');
          setButtonText('Get location');
        },
        {enableHighAccuracy: true, timeout: 30000, maximumAge: 10000},
      );
    } catch (err) {
      console.log('fetchLocation error:', err);
      setButtonText('Get location');
      api?.showToast('Something went wrong.');
    } finally {
      setIsFetchingLocation(false);
    }
  };

  useEffect(() => {
    const getRegion = async () => {
      if (api) {
        const region = await api.getSelectedRegion();
        if (region) {
          setSelectedRegion(region);
          setValue('region', region.name);
          handleChange('region', region.name);
        }
      }
    };

    getRegion();
  }, []);

  // Handle field change
  const handleChange = (field: string, value: string) => {
    // setFormValues(prev => ({
    //   ...prev,
    //   [field]: value,
    // }));
  };

  const downloadImageToFile = async (imageUrl: any, index: any) => {
    const fileName = `existing_img_${index}.jpg`;
    const localPath = `${RNFS.TemporaryDirectoryPath}/${fileName}`;

    try {
      const downloadResult = await RNFS.downloadFile({
        fromUrl: imageUrl,
        toFile: localPath,
      }).promise;

      if (downloadResult.statusCode === 200) {
        return 'file://' + localPath;
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Failed to download image:', error);
      return null;
    }
  };
  const getImageUrl = (cusCode: string, imageName: any) => {
    return `${IMAGE_API_BASE_URL}/${cusCode}/${imageName}`;
  };

  const onSubmit = async (data: any) => {
    console.log('Form Data:', data);
    console.log('Milk Data:', {isMilkSelected, milkLiters, milkMilliliters});
    console.log(photos, photoUri, 'photoUriLogs');

    const randomSuffix = Math.floor(1000 + Math.random() * 9000);
    const customerCode = customerId
      ? customerData?.customerCode
      : `${data.phoneNumber}_${Date.now()}_${randomSuffix}`;
    const customerProducts = Object.entries(customerProd).map(
      ([productId, {liters, milliliters}]: any) => {
        const quantity = liters + milliliters;
        const startDate = new Date().toISOString();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 1);

        const productEntry: any = {
          productId,
          quantity,
          startDate,
          endDate: endDate.toISOString(),
        };

        if (customerId) {
          productEntry.customerId = customerId;
        }

        return productEntry;
      },
    );
    let routeId;
    if (empData) {
      routeId = empData?.employeeRoutes[0]?.id;
    }
    const customerData1 = {
      name: data.name,
      mobile: parseInt(data.phoneNumber, 10),
      customerCode,
      region: data.region,
      floorNumber: data.floorNumber || '',
      longitude: location
        ? location.longitude.toString()
        : customerData?.longitude || '',
      lattitude: location
        ? location.latitude.toString()
        : customerData?.lattitude || '',
      email: data.email || '',
      doorNumber: data.doorNumber || '',
      houseType: data.houseType || '',
      customerProducts,
      routeId,
    };

    console.log(customerData1, customerId, 'sendtoupdateImage');
    // return;
    try {
      if (customerId) {
        const updatedCustomerData = {
          ...customerData1,
          customerId,
        };

        const updateRes = await dispatch(
          updateCustomerApi({customerId, customerData1: updatedCustomerData}),
        ).unwrap();

        if (updateRes?.success && photos.length > 0 && customerCode) {
          const formData = new FormData();

          for (let i = 0; i < photos.length; i++) {
            const photo = photos[i];

            if (photo.isNew) {
              formData.append('request', {
                uri: photo.uri,
                name: photo.fileName || `new_image_${i}.jpg`,
                type: photo.type || 'image/jpeg',
              });
            } else {
              const imageUrl = getImageUrl(customerCode, photo.imageName);
              const localUri = await downloadImageToFile(imageUrl, i);

              if (localUri) {
                formData.append('request', {
                  uri: localUri,
                  name: photo.imageName,
                  type: photo.imageType || 'image/jpeg',
                });
              }
            }
          }

          await dispatch(
            uploadCustomerImagesApi({customerCode, formData}),
          ).unwrap();
        }
      } else {
        const res = await dispatch(addCustomerApi(customerData1)).unwrap();

        if (res?.success && res?.data && photos.length > 0) {
          const formData = new FormData();
          photos.forEach((photo, index) => {
            formData.append('request', {
              uri: photo.uri,
              name: `customer_${customerData1.customerCode}_img_${index}.jpg`,
              type: photo.type || 'image/jpeg',
            });
          });

          await dispatch(
            uploadCustomerImagesApi({
              customerCode: customerData1.customerCode,
              formData,
            }),
          ).unwrap();
        }
      }

      api?.showToast('Customer saved successfully');
      dispatch(resetCustomerData())
      navigation.goBack();
    } catch (error) {
      console.error('Error submitting customer:', error);
      api?.showToast('Failed to save customer');
    }
  };
  const [modalVisible, setModalVisible] = useState(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    message: string;
  }>({title: '', message: ''});

  useEffect(() => {
    const isAddSuccess = operationStatus.add === 'succeeded';
    const isUpdateSuccess = operationStatus.update === 'succeeded';
    const isCustomerSuccess = isAddSuccess || isUpdateSuccess;
    const isImageSaveSuccess = operationStatus.imagesave === 'succeeded';

    const shouldUploadImages = photos.length > 0;

    if (isCustomerSuccess && !shouldUploadImages) {
      showSuccessModal();
    }

    if (isCustomerSuccess && shouldUploadImages && isImageSaveSuccess) {
      showSuccessModal();
    }

    if (
      (operationStatus.add === 'failed' ||
        operationStatus.update === 'failed') &&
      error
    ) {
      const errorMessage = Array.isArray(error?.errors)
        ? error.errors[0]
        : typeof error === 'string'
        ? error
        : 'Something went wrong';

      api?.showToast(
        `Failed to ${
          operationStatus.add === 'failed' ? 'add' : 'edit'
        } customer: ${errorMessage}`,
      );
    }
  }, [
    operationStatus.add,
    operationStatus.update,
    operationStatus.imagesave,
    error,
    photos.length,
  ]);

  const showSuccessModal = () => {
    reset();
    setModalContent({
      title: 'Success',
      message:
        operationStatus.add === 'succeeded'
          ? 'Customer added successfully'
          : 'Customer edited successfully',
    });
    setModalVisible(true);
    dispatch(resetCustomerApiStatus());
    dispatch(resetCustomerData());
  };

  const [deleteIndex, setDeleteIndex] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleTakePhoto = async () => {
    if (api) {
      const hasPermission = await api.requestCameraPermission();
      if (hasPermission) {
        launchCamera(
          {
            mediaType: 'photo',
            includeBase64: false,
            maxHeight: 1200,
            maxWidth: 1200,
            quality: 0.5,
          },
          response => {
            if (response.didCancel) {
              console.log('User cancelled camera');
            } else if (response.errorCode) {
              console.log('Camera Error:', response.errorMessage);
            } else if (response.assets && response.assets.length > 0) {
              const asset = response?.assets?.[0];
              if (asset?.uri) {
                const newPhoto = {
                  uri: asset.uri,
                  isNew: true,
                  fileName: asset.fileName || `photo_${Date.now()}.jpg`,
                  type: asset.type || 'image/jpeg',
                };
                const newPhotos = [...photos, newPhoto];
                setPhotos(newPhotos);
                setPhotoUri(asset.uri);

                // if (!formValues.photo) {
                //   handleChange('photo', asset.uri);
                // }
              }
            }
          },
        );
      } else {
        Alert.alert(
          'Permission Denied',
          'Camera permission is required to take photos',
        );
      }
    }
  };

  const houseTypeOptions = [
    {label: 'Apartment', value: 'apartment'},
    {label: 'Individual House', value: 'individual'},
  ];

  const handleMilkSelectionChange = (
    prod: Record<
      string,
      {selected: boolean; liters: number; milliliters: number}
    >,
  ) => {
    setCustomerProd(prod);

    const hasValidProduct = Object.values(prod).some(
      p => (p.liters || 0) + (p.milliliters || 0) > 0,
    );

    setValue('customerProdValid', hasValidProduct, {shouldValidate: true});
  };
  const getStoredEmployeeData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('employeeData');
      if (storedData) {
        const data = JSON.parse(storedData);
        console.log(data, 'Parsed employee data');
        setempData(data);
        return data;
      } else {
        console.log('No data found');
      }
    } catch (error) {
      console.error('Error reading employee data:', error);
    }
  };
  useEffect(() => {
    getStoredEmployeeData();
  }, []);
  useEffect(() => {
    if (empData?.employeeId) {
      dispatch(fetchRotes({empId: empData.employeeId}));
    }
  }, [empData]);
  useEffect(() => {
    if (fetchLoad === 'succeeded') {
      dispatch(resetRegionApiStatus());
    }
  }, [fetchLoad]);
  return (
    <View style={styles.container}>
      <Header
        title={
          viewOnly
            ? 'Customer Details'
            : customerId
            ? 'Edit Customer'
            : 'Add Customer'
        }
      />

      {/* <View
        style={{flexDirection: 'row', justifyContent: 'center', margin: 15}}>
        <Image
          source={imagePath.person_Icon}
          style={{
            width: 20,
            height: 20,
            tintColor: '#507806',
            resizeMode: 'cover',
          }}
        />
        <Text
          style={{
            fontSize: 16,
            fontFamily: FontFamily.REGULAR,
            color: Colors.greenColour,
            top: 3,
            marginLeft: 8,
          }}>
          Fill customer Details...
        </Text>
      </View> */}

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}>
        {/* Region Field - disabled text input */}
        <CustomInputController
          control={control}
          name="region"
          label="Region"
          editable={false}
          disabled={true}
        />

        {/* <TouchableOpacity
          style={styles.locationButton}
          onPress={fetchLocation}
          disabled={isFetchingLocation}>
          <Text
            style={[
              styles.locationButtonText,
              errors.location ? styles.errorText : null,
            ]}>
            {buttonText || 'Get Location'}
          </Text>
        </TouchableOpacity> */}
        {/* {errors.location && (
  <Text style={styles.tooltip}>{errors.location.message}</Text>
)} */}
        {fetchedAddress !== '' && !isEditMode && (
          <>
            <View style={styles.addressContainer}>
              <Text style={styles.addressText}>{fetchedAddress}</Text>
              <TouchableOpacity
                onPress={() => {
                  setIsEditMode(true), setButtonText('Fetch Location Again');
                }}>
                <View style={styles.editIconWrapper}>
                  <Image
                    source={imagePath.edit_location}
                    style={styles.editIcon}
                    resizeMode="cover"
                  />
                </View>
              </TouchableOpacity>
            </View>
          </>
        )}

        {(isEditMode || fetchedAddress === '') && (
          <TouchableOpacity
            style={styles.locationButton}
            onPress={fetchLocation}
            disabled={isFetchingLocation}>
            <Text
              style={[
                styles.locationButtonText,
                errors.location ? styles.errorText : null,
              ]}>
              {buttonText || 'Get Location'}
            </Text>
          </TouchableOpacity>
        )}

        {errors.location && (
          <Text style={styles.tooltip}>{errors.location.message}</Text>
        )}

        <Controller
          control={control}
          name="houseType"
          rules={{required: 'House type is required'}}
          render={({field: {onChange, value}, fieldState: {error}}) => (
            <Dropdown
              label="House Type"
              value={value}
              options={houseTypeOptions}
              onSelect={onChange}
              error={error?.message}
            />
          )}
        />

        {watch('houseType') === 'apartment' && (
          <CustomInputController
            control={control}
            name="floorNumber"
            label="Floor Number"
            keyboardType="number-pad"
            rules={{required: 'Floor number is required'}}
          />
        )}

        {(watch('houseType') === 'apartment' ||
          watch('houseType') === 'individual') && (
          <CustomInputController
            control={control}
            name="doorNumber"
            label="Door Number"
            rules={{required: 'Door number is required'}}
          />
        )}

        <PhotoCapture
          photoUri={photoUri}
          photos={photos}
          onCapture={handleTakePhoto}
          customerCode={customerCode}
          // onDeletePhoto={index => {
          //   Alert.alert(
          //     'Delete Photo',
          //     'Are you sure you want to delete this photo?',
          //     [
          //       {text: 'Cancel', style: 'cancel'},
          //       {
          //         text: 'Yes',
          //         style: 'destructive',
          //         onPress: () => {
          //           const newPhotos = photos.filter((_, i) => i !== index);
          //           setPhotos(newPhotos);
          //           if (newPhotos.length > 0) {
          //             setPhotoUri(newPhotos[newPhotos.length - 1]?.uri || null);
          //           } else {
          //             setPhotoUri(null);
          //           }
          //         },
          //       },
          //     ],
          //   );
          // }}
          onDeletePhoto={index => {
            setDeleteIndex(index);
            setShowDeleteModal(true);
          }}
        />

        <CustomInputController
          control={control}
          name="name"
          label="Customer Name"
          rules={{required: 'Name is required'}}
        />

        <CustomInputController
          control={control}
          name="phoneNumber"
          label="Phone Number"
          keyboardType="phone-pad"
          rules={{
            required: 'Phone number is required',
            pattern: {
              value: /^[0-9]{10}$/,
              message: 'Phone number must be 10 digits',
            },
          }}
        />
        {/* Milk Selector */}
        <MilkSelector
          onMilkSelectionChange={handleMilkSelectionChange}
          value={customerProd}
        />
        {errors.customerProdValid && (
          <Text style={styles.tooltip}>{errors.customerProdValid.message}</Text>
        )}

        {/* Submit Button */}
        {!viewOnly && (
          <TouchableOpacity
            style={[
              styles.submitButton,
              status === 'loading' ? styles.disabledButton : null,
            ]}
            onPress={handleSubmit(onSubmit)}
            disabled={status === 'loading'}>
            {status === 'loading' ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>
                {customerId ? 'Save Changes' : 'Add Customer'}
              </Text>
            )}
          </TouchableOpacity>
        )}
      </ScrollView>
      {/* <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <Pressable
          style={styles.modalContainer}
          onPress={() => {
            setModalVisible(false);
            if (modalContent.title === 'Success') {
              navigation.goBack();
            }
          }}>
          <Pressable
            style={styles.modalContent}
            onPress={e => e.stopPropagation()}>
            <Text style={styles.modalTitle}>{modalContent.title}</Text>
            <Text style={styles.modalMessage}>{modalContent.message}</Text>
            <TouchableOpacity
              onPress={() => {
                setModalVisible(false);
                if (modalContent.title === 'Success') {
                  navigation.goBack();
                }
              }}
              style={styles.modalButton}>
              <Text style={styles.modalButtonText}>OK</Text>
            </TouchableOpacity>
          </Pressable>
        </Pressable>
      </Modal> */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={showDeleteModal}
        onRequestClose={() => setShowDeleteModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalBox}>
            <Text style={styles.modalTitle}>Delete Photo</Text>
            <Text style={styles.modalMessage}>
              Are you sure you want to delete this photo?
            </Text>

            <View style={styles.modalActions}>
              <TouchableOpacity
                onPress={() => setShowDeleteModal(false)}
                style={[styles.modalButton, styles.cancelButton]}>
                <Text style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  const newPhotos = photos.filter((_, i) => i !== deleteIndex);
                  setPhotos(newPhotos);
                  setPhotoUri(
                    newPhotos.length > 0
                      ? newPhotos[newPhotos.length - 1]?.uri
                      : null,
                  );
                  setShowDeleteModal(false);
                }}
                style={[styles.modalButton, styles.deleteButton]}>
                <Text style={styles.deleteText}>Yes, Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  locationButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  locationButtonText: {
    color: Colors.white,
    fontSize: 14,
    fontFamily: FontFamily.REGULAR,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: Colors.secondary,
  },
  errorText: {
    color: 'black',
    fontSize: 14,
    marginTop: 4,
  },
  inputG: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: Colors.dark,
    paddingTop: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontFamily: FontFamily.REGULAR,
    color: Colors.listSeconary,
    marginBottom: 6,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderColor: Colors.greyBackground,
    fontSize: 16,
    color: Colors.dark,
    backgroundColor: Colors.white,
  },
  tooltip: {
    color: Colors.danger,
    fontSize: 14,
    marginBottom: 4,
    marginLeft: 4,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    padding: 5,
    color: Colors.dark,
    borderRadius: 8,
    borderColor: Colors.greyBackground,
  },

  addressText: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
    flexShrink: 1,
  },

  editIconWrapper: {
    borderWidth: 1,
    borderColor: 'black',
    borderRadius: 10,
    padding: 4, // Adds space between image and border
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
  },

  editIcon: {
    width: 18, // Reduced size
    height: 18,
    tintColor: 'black',
    borderRadius: 8, // Optional: keeps icon itself rounded
  },

  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  modalMessage: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalBox: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    elevation: 5,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },

  cancelButton: {
    backgroundColor: '#e0e0e0',
  },
  deleteButton: {
    backgroundColor: '#d9534f',
  },
  cancelText: {
    color: '#333',
    fontWeight: 'bold',
  },
  deleteText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CustomerDetailScreen;
