import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
  Image,
} from 'react-native';
import { Colors } from '../../constants/colors';

interface PhoneNumberBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  phoneNumbers: string[];
  onSelectNumber: (number: string) => void;
}

const PhoneNumberBottomSheet: React.FC<PhoneNumberBottomSheetProps> = ({
  visible,
  onClose,
  phoneNumbers,
  onSelectNumber,
}) => {
  return (
    <Modal
      transparent
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Pressable style={styles.backdrop} onPress={onClose} />
        <View style={styles.bottomSheet}>
          <View style={styles.header}>
            <View style={styles.googleContainer}>
              <Text style={styles.googleIcon}>6AM</Text>
            </View>
            <Text style={styles.title}>Choose a phone number</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeIcon}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.description}>
            Select a phone number to use with SixAm Delivery App. This number will be used for delivery notifications.
          </Text>
          
          <Text style={styles.subDescription}>
            Your phone number will only be used for delivery-related communications.
          </Text>
          
          {phoneNumbers.map((number, index) => (
            <TouchableOpacity 
              key={index} 
              style={styles.numberItem}
              onPress={() => onSelectNumber(number)}
            >
              <View style={styles.phoneIconContainer}>
                <Text style={styles.phoneIcon}>📱</Text>
              </View>
              <Text style={styles.phoneNumber}>{number}</Text>
            </TouchableOpacity>
          ))}
          
          <Text style={styles.footer}>
            You can update your delivery preferences in your <Text style={styles.link}>account settings</Text>.
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '70%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  googleContainer: {
    width: 35,
    height: 35,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  googleIcon: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  title: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#202124',
  },
  closeButton: {
    padding: 4,
  },
  closeIcon: {
    fontSize: 18,
    color: '#5f6368',
  },
  description: {
    fontSize: 14,
    color: '#202124',
    marginBottom: 12,
    lineHeight: 20,
  },
  subDescription: {
    fontSize: 14,
    color: '#5f6368',
    marginBottom: 16,
    lineHeight: 20,
  },
  numberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  phoneIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f1f3f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  phoneIcon: {
    fontSize: 16,
  },
  phoneNumber: {
    fontSize: 16,
    color: '#202124',
  },
  footer: {
    fontSize: 14,
    color: '#5f6368',
    marginTop: 16,
    lineHeight: 20,
  },
  link: {
    color: '#1a73e8',
  },
});

export default PhoneNumberBottomSheet;
